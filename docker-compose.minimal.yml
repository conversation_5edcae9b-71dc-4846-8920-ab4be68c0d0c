version: '3.8'

services:
  # Database
  db:
    image: postgres:14-alpine
    container_name: spheroseg-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: spheroseg
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

  # Redis
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis
    restart: always
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: spheroseg-rabbitmq
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RA<PERSON><PERSON><PERSON><PERSON>_DEFAULT_PASS: guest
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - spheroseg-network

  # Application (all-in-one for dev)
  app:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: spheroseg-app
    restart: always
    ports:
      - "3000:3000"
      - "5001:5001"
    environment:
      DATABASE_URL: **************************************/spheroseg
      REDIS_URL: redis://redis:6379
      RABBITMQ_URL: amqp://guest:guest@rabbitmq:5672
      NODE_ENV: development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - spheroseg-network

volumes:
  postgres_data:

networks:
  spheroseg-network:
    driver: bridge