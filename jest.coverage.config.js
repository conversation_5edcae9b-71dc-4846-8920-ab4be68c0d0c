/**
 * Jest Coverage Configuration
 * 
 * Enforces test coverage thresholds across the monorepo
 */

module.exports = {
  // Global coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 85,
      statements: 85,
    },
    // Package-specific thresholds
    './packages/backend/src/': {
      branches: 75,
      functions: 80,
      lines: 85,
      statements: 85,
    },
    './packages/frontend/src/': {
      branches: 70,
      functions: 75,
      lines: 80,
      statements: 80,
    },
    './packages/shared/src/': {
      branches: 90,
      functions: 90,
      lines: 95,
      statements: 95,
    },
    './packages/types/src/': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    // Critical paths with stricter requirements
    './packages/backend/src/services/authService.ts': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    './packages/backend/src/services/segmentationService.ts': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    './packages/frontend/src/pages/segmentation/': {
      branches: 75,
      functions: 80,
      lines: 85,
      statements: 85,
    },
  },
  
  // Files to collect coverage from
  collectCoverageFrom: [
    'packages/*/src/**/*.{ts,tsx,js,jsx}',
    '!packages/*/src/**/*.d.ts',
    '!packages/*/src/**/types.ts',
    '!packages/*/src/**/*.stories.{ts,tsx}',
    '!packages/*/src/**/*.test.{ts,tsx}',
    '!packages/*/src/**/*.spec.{ts,tsx}',
    '!packages/*/src/**/__tests__/**',
    '!packages/*/src/**/__mocks__/**',
    '!packages/*/src/test-utils/**',
    '!packages/ml/**', // Python code covered separately
    '!packages/*/src/migrations/**',
    '!packages/*/src/scripts/**',
    '!packages/frontend/src/vite-env.d.ts',
    '!packages/frontend/src/main.tsx', // Entry point
    '!packages/backend/src/server.ts', // Entry point
  ],
  
  // Coverage reporters
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json',
    'json-summary',
    'cobertura', // For CI/CD integration
  ],
  
  // Coverage directory
  coverageDirectory: '<rootDir>/coverage',
  
  // Additional coverage options
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/.next/',
    '/coverage/',
    '\\.config\\.(js|ts)$',
    '/scripts/',
    '/cypress/',
    '/playwright/',
  ],
};