#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Pre-push hook for Spheroseg
# Runs coverage checks before allowing push to remote

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo "${BOLD}${BLUE}🔍 Pre-push Coverage Check${NC}"
echo "${BLUE}════════════════════════════${NC}"

# Get the remote branch
BRANCH=$(git rev-parse --abbrev-ref HEAD)
PROTECTED_BRANCHES="main master develop dev"

# Check if pushing to protected branch
IS_PROTECTED=false
for protected in $PROTECTED_BRANCHES; do
  if [ "$BRANCH" = "$protected" ]; then
    IS_PROTECTED=true
    break
  fi
done

if [ "$IS_PROTECTED" = true ]; then
  echo "${YELLOW}⚠️  Pushing to protected branch: ${<PERSON><PERSON>CH}${NC}"
  echo "${BLUE}Running comprehensive coverage check...${NC}"
  
  # Run coverage check
  if npm run coverage:check; then
    echo "${GREEN}✅ Coverage requirements met!${NC}"
  else
    echo ""
    echo "${RED}${BOLD}❌ Coverage Check Failed${NC}"
    echo "${RED}Cannot push to ${BRANCH} with coverage below thresholds${NC}"
    echo ""
    echo "${YELLOW}Options:${NC}"
    echo "${YELLOW}1. Add more tests to increase coverage${NC}"
    echo "${YELLOW}2. Push to a feature branch instead${NC}"
    echo "${YELLOW}3. Use --no-verify to bypass (not recommended)${NC}"
    exit 1
  fi
else
  echo "${BLUE}Pushing to feature branch: ${BRANCH}${NC}"
  echo "${BLUE}Skipping full coverage check (will run in CI)${NC}"
  
  # Run quick test check
  echo "${BLUE}Running quick test validation...${NC}"
  if npm run test:quick 2>/dev/null || npm test -- --passWithNoTests; then
    echo "${GREEN}✅ Tests passed!${NC}"
  else
    echo "${RED}❌ Tests failed! Fix tests before pushing.${NC}"
    exit 1
  fi
fi

echo "${GREEN}${BOLD}✅ Pre-push checks passed!${NC}"
echo "${BLUE}════════════════════════════${NC}"