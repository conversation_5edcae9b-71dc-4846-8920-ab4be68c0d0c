name: Test Coverage

on:
  push:
    branches: [main, dev, develop]
  pull_request:
    branches: [main, dev, develop]

jobs:
  coverage:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci
          npm run bootstrap
      
      - name: Run backend tests with coverage
        working-directory: ./packages/backend
        run: |
          npm run test:coverage
        env:
          NODE_ENV: test
          JWT_SECRET: test-secret
          DATABASE_URL: postgresql://test:test@localhost:5432/test
      
      - name: Run frontend tests with coverage
        working-directory: ./packages/frontend
        run: |
          npm run test:coverage
        env:
          NODE_ENV: test
      
      - name: Run shared tests with coverage
        working-directory: ./packages/shared
        run: |
          npm run test:coverage
        env:
          NODE_ENV: test
      
      - name: Run types tests with coverage
        working-directory: ./packages/types
        run: |
          npm run test:coverage
        env:
          NODE_ENV: test
      
      - name: Check coverage thresholds
        run: |
          node scripts/coverage-check.js
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: |
            ./packages/backend/coverage/lcov.info
            ./packages/frontend/coverage/lcov.info
            ./packages/shared/coverage/lcov.info
            ./packages/types/coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: true
      
      - name: Generate coverage report comment
        if: github.event_name == 'pull_request'
        uses: romeovs/lcov-reporter-action@v0.3.1
        with:
          lcov-file: ./coverage/lcov.info
          github-token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Archive coverage reports
        uses: actions/upload-artifact@v3
        with:
          name: coverage-reports
          path: |
            packages/*/coverage/
            coverage/
          retention-days: 7
      
      - name: Check coverage decrease
        if: github.event_name == 'pull_request'
        run: |
          # Compare coverage with base branch
          echo "Checking for coverage decrease..."
          # This would compare with stored coverage from main branch
          # Implementation depends on how you store historical coverage

  coverage-badge:
    needs: coverage
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download coverage artifacts
        uses: actions/download-artifact@v3
        with:
          name: coverage-reports
          path: coverage-reports
      
      - name: Update coverage badges
        run: |
          # Read badges.json and update README badges
          if [ -f "coverage-reports/coverage/badges.json" ]; then
            BADGES=$(cat coverage-reports/coverage/badges.json)
            echo "Coverage badges data: $BADGES"
            # Update README.md badges here
          fi
      
      - name: Commit badge updates
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: 'chore: update coverage badges'
          file_pattern: README.md
          skip_dirty_check: true