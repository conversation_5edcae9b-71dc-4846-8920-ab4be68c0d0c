# Type Safety Migration Guide

This guide provides instructions for migrating away from `any` types to improve type safety across the SpherosegV4 codebase.

## Overview

The codebase currently has several instances of `any` types that reduce type safety. This guide provides patterns and utilities to replace these with proper types.

## New Type Safety Utilities

### Backend Type Safety Helpers

Location: `/packages/backend/src/utils/typeSafetyHelpers.ts`

#### Key Functions:

1. **Typed Function Wrappers**
```typescript
// Instead of:
function withErrorHandling<T extends (...args: any[]) => Promise<any>>

// Use:
import { AsyncFunction, createTypedErrorHandler } from '@/utils/typeSafetyHelpers';

function withErrorHandling<TParams extends readonly unknown[], TReturn>(
  serviceName: string,
  methodName: string,
  fn: AsyncFunction<TParams, TReturn>
): AsyncFunction<TParams, TReturn>
```

2. **Type Guards**
```typescript
// Instead of:
if (error) { /* handle error */ }

// Use:
import { isError, isDefined } from '@/utils/typeSafetyHelpers';

if (isError(error)) {
  // error is now typed as Error
}

if (isDefined(value)) {
  // value is guaranteed to be non-null/undefined
}
```

3. **Typed Object Utilities**
```typescript
// Instead of:
Object.keys(obj).forEach((key: any) => { ... })

// Use:
import { typedKeys, typedEntries } from '@/utils/typeSafetyHelpers';

typedKeys(obj).forEach((key) => {
  // key is properly typed as keyof typeof obj
});
```

### Database Type Safety

Location: `/packages/backend/src/db/typeSafeQueries.ts`

#### Migration Examples:

1. **Basic Queries**
```typescript
// Instead of:
const result = await db.query('SELECT * FROM users WHERE id = $1', [userId]);
const user: any = result.rows[0];

// Use:
import { queryOne } from '@/db/typeSafeQueries';
import { User } from '@spheroseg/types';

const user = await queryOne<User>(
  'SELECT * FROM users WHERE id = $1',
  [userId]
);
// user is typed as User | null
```

2. **Insert Operations**
```typescript
// Instead of:
const result = await db.query(
  'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING *',
  [name, userId]
);
const project: any = result.rows[0];

// Use:
import { insert } from '@/db/typeSafeQueries';
import { Project } from '@spheroseg/types';

const project = await insert<Project>('projects', {
  name,
  user_id: userId
});
// project is typed as Project
```

3. **Transactions**
```typescript
// Instead of:
const client = await db.connect();
try {
  await client.query('BEGIN');
  // ... operations
  await client.query('COMMIT');
} catch (error) {
  await client.query('ROLLBACK');
}

// Use:
import { transaction } from '@/db/typeSafeQueries';

const result = await transaction(async (client) => {
  const user = await client.query<User>(...);
  const project = await client.query<Project>(...);
  return { user, project };
});
```

### Frontend Type Safety

Location: `/packages/frontend/src/utils/typeSafeComponents.tsx`

#### Component Patterns:

1. **Lazy Loading**
```typescript
// Instead of:
const MyComponent = lazy(() => import('./MyComponent')) as any;

// Use:
import { createLazyComponent } from '@/utils/typeSafeComponents';

const MyComponent = createLazyComponent(() => import('./MyComponent'));
```

2. **Higher-Order Components**
```typescript
// Instead of:
function withAuth(Component: any) {
  return (props: any) => { ... };
}

// Use:
import { createHOC } from '@/utils/typeSafeComponents';

const withAuth = createHOC<AuthProps>('withAuth', (Component) => {
  return (props) => {
    // props is properly typed
    return <Component {...props} />;
  };
});
```

3. **Context Creation**
```typescript
// Instead of:
const MyContext = createContext<any>(undefined);

// Use:
import { createTypedContext } from '@/utils/typeSafeComponents';

const { Provider, useContext } = createTypedContext<MyContextType>(
  defaultValue,
  'MyContext'
);
```

## Migration Steps

### Phase 1: Test Files (Low Risk)

1. Start with test files where `any` is commonly used for mocks
2. Replace mock creation functions:

```typescript
// Instead of:
function createMockUser(overrides: Partial<any> = {})

// Use:
import { User } from '@spheroseg/types';
function createMockUser(overrides: Partial<User> = {})
```

### Phase 2: Utility Functions

1. Update utility functions to use generic constraints:

```typescript
// Instead of:
export function processData(data: any): any

// Use:
export function processData<T>(data: T): T
```

### Phase 3: Database Queries

1. Migrate database queries to use typed query functions
2. Define result types for all queries
3. Use type guards for runtime validation

### Phase 4: API Handlers

1. Type request and response objects properly
2. Use validation middleware that preserves types
3. Leverage TypeScript's strict mode

### Phase 5: Frontend Components

1. Replace `any` in component props
2. Use proper event handler types
3. Type refs and context values correctly

## Common Patterns to Replace

### 1. Function Parameters

```typescript
// ❌ Avoid
function handleData(data: any) { }

// ✅ Prefer
function handleData<T>(data: T) { }
// or
function handleData(data: unknown) { } // with runtime checks
// or
function handleData(data: SpecificType) { }
```

### 2. Array Operations

```typescript
// ❌ Avoid
const items: any[] = [];

// ✅ Prefer
const items: SpecificType[] = [];
// or
const items: unknown[] = []; // with type guards
```

### 3. Object Types

```typescript
// ❌ Avoid
const config: any = {};

// ✅ Prefer
interface Config {
  // define properties
}
const config: Config = {};
// or
const config: Record<string, unknown> = {};
```

### 4. Promise Types

```typescript
// ❌ Avoid
async function fetchData(): Promise<any>

// ✅ Prefer
async function fetchData(): Promise<SpecificType>
// or
async function fetchData<T>(): Promise<T>
```

## Validation and Runtime Checks

When migrating from `any`, add runtime validation:

```typescript
import { z } from 'zod';

// Define schema
const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string(),
});

// Use in API handler
export async function getUser(id: string) {
  const data = await fetchUserData(id);
  
  // Validate at runtime
  const user = UserSchema.parse(data);
  // user is now properly typed
  
  return user;
}
```

## Tools and Scripts

### ESLint Rules

Add these rules to catch `any` usage:

```json
{
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unsafe-assignment": "error",
    "@typescript-eslint/no-unsafe-member-access": "error",
    "@typescript-eslint/no-unsafe-call": "error",
    "@typescript-eslint/no-unsafe-return": "error"
  }
}
```

### Type Coverage

Check type coverage with:

```bash
npx type-coverage --detail
```

Aim for >95% type coverage.

## Benefits

1. **Better IntelliSense**: IDEs can provide better autocomplete
2. **Catch Bugs Earlier**: Type errors caught at compile time
3. **Improved Refactoring**: Safer code changes with compiler assistance
4. **Documentation**: Types serve as inline documentation
5. **Runtime Safety**: Combined with validation, prevents runtime errors

## Gradual Migration

1. Enable strict mode in `tsconfig.json`
2. Fix errors file by file
3. Add `// @ts-expect-error` for temporary suppressions
4. Remove suppressions as you fix types
5. Run type coverage reports regularly

## Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)
- [Type Guards](https://www.typescriptlang.org/docs/handbook/2/narrowing.html)
- [Generics](https://www.typescriptlang.org/docs/handbook/2/generics.html)
- [Utility Types](https://www.typescriptlang.org/docs/handbook/utility-types.html)