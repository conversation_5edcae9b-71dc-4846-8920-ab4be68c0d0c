# Test Coverage Guide

Comprehensive guide for maintaining and improving test coverage in SpherosegV4.

## Coverage Requirements

### Global Thresholds
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 85%
- **Statements**: 85%

### Package-Specific Thresholds

| Package | Branches | Functions | Lines | Statements |
|---------|----------|-----------|-------|------------|
| Backend | 75% | 80% | 85% | 85% |
| Frontend | 70% | 75% | 80% | 80% |
| Shared | 90% | 90% | 95% | 95% |
| Types | 100% | 100% | 100% | 100% |

### Critical Path Requirements

Critical services have higher requirements:

- **authService.ts**: 90% branches, 95% functions/lines/statements
- **segmentationService.ts**: 85% branches, 90% functions/lines/statements
- **Segmentation UI**: 75% branches, 80% functions, 85% lines/statements

## Running Coverage

### Local Coverage Checks

```bash
# Check all packages
npm run coverage:check

# Check specific package
npm run coverage:check backend

# Generate HTML reports
npm run coverage:report

# View coverage in browser
npm run coverage:view
```

### Package-Specific Coverage

```bash
# Backend
cd packages/backend
npm run test:coverage

# Frontend
cd packages/frontend
npm run test:coverage

# View HTML report
open coverage/index.html
```

## Understanding Coverage Reports

### Coverage Metrics

1. **Branches**: All possible paths through conditional logic
   ```typescript
   // Each condition creates branches
   if (user && user.isActive) { // 4 branches: TT, TF, FT, FF
     return 'active';
   }
   ```

2. **Functions**: All defined functions are called
   ```typescript
   // This function needs to be called in tests
   function calculateTotal(items: Item[]) {
     return items.reduce((sum, item) => sum + item.price, 0);
   }
   ```

3. **Lines**: All executable lines are run
   ```typescript
   function greet(name: string) {
     const greeting = `Hello ${name}`; // This line must execute
     console.log(greeting);            // This line must execute
     return greeting;                  // This line must execute
   }
   ```

4. **Statements**: All statements are executed
   ```typescript
   // Each statement must be covered
   let total = 0;           // Statement 1
   total += price;          // Statement 2
   total *= (1 + tax);      // Statement 3
   ```

### Reading Coverage Reports

#### Console Output
```
--------------------|---------|----------|---------|---------|-------------------
File                | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s
--------------------|---------|----------|---------|---------|-------------------
All files           |   85.24 |    78.95 |   82.35 |   85.24 |
 authService.ts     |   95.12 |    90.00 |   94.44 |   95.12 | 45-47,89
 userService.ts     |   78.26 |    70.00 |   75.00 |   78.26 | 23,56-61,102-105
--------------------|---------|----------|---------|---------|-------------------
```

#### HTML Reports
Open `coverage/index.html` to see:
- File-by-file breakdown
- Line-by-line coverage highlighting
- Uncovered code in red
- Partially covered in yellow
- Fully covered in green

## Writing Tests for Coverage

### 1. Test All Branches

```typescript
// Code to test
function getDiscount(user: User): number {
  if (user.isPremium) {
    return user.loyaltyYears > 5 ? 0.20 : 0.10;
  }
  return 0;
}

// Tests needed
describe('getDiscount', () => {
  it('returns 20% for premium users with >5 years', () => {
    const user = { isPremium: true, loyaltyYears: 6 };
    expect(getDiscount(user)).toBe(0.20);
  });

  it('returns 10% for premium users with <=5 years', () => {
    const user = { isPremium: true, loyaltyYears: 3 };
    expect(getDiscount(user)).toBe(0.10);
  });

  it('returns 0% for non-premium users', () => {
    const user = { isPremium: false, loyaltyYears: 10 };
    expect(getDiscount(user)).toBe(0);
  });
});
```

### 2. Test Error Paths

```typescript
// Don't forget error cases
describe('processPayment', () => {
  it('handles network errors', async () => {
    mockApi.post.mockRejectedValue(new Error('Network error'));
    
    await expect(processPayment(payment))
      .rejects.toThrow('Payment failed');
  });

  it('handles invalid card', async () => {
    mockApi.post.mockResolvedValue({ 
      status: 400, 
      error: 'Invalid card' 
    });
    
    await expect(processPayment(payment))
      .rejects.toThrow('Invalid card');
  });
});
```

### 3. Test Edge Cases

```typescript
describe('calculateAverage', () => {
  it('handles empty array', () => {
    expect(calculateAverage([])).toBe(0);
  });

  it('handles single value', () => {
    expect(calculateAverage([5])).toBe(5);
  });

  it('handles negative numbers', () => {
    expect(calculateAverage([-1, -2, -3])).toBe(-2);
  });
});
```

## Improving Coverage

### 1. Find Uncovered Code

```bash
# Generate detailed report
npm run test:coverage

# Look for uncovered lines in HTML report
open coverage/index.html
```

### 2. Common Coverage Gaps

#### Unhandled Error Cases
```typescript
// Often uncovered
try {
  await riskyOperation();
} catch (error) {
  // This catch block might not be covered
  logger.error('Operation failed', error);
  throw new ServiceError('Operation failed');
}
```

#### Default Parameters
```typescript
// Default values often uncovered
function greet(name = 'World') {
  return `Hello ${name}`;
}

// Test both with and without parameter
expect(greet()).toBe('Hello World');
expect(greet('Alice')).toBe('Hello Alice');
```

#### Early Returns
```typescript
function process(data: Data) {
  if (!data) return null; // Often missed
  
  if (!data.isValid) {
    logger.warn('Invalid data');
    return null; // Often missed
  }
  
  return transform(data);
}
```

### 3. Strategic Testing

Focus on:
1. **Critical paths**: Authentication, payments, data processing
2. **Complex logic**: Algorithms, state machines, validators
3. **Error handling**: All catch blocks and error paths
4. **Edge cases**: Boundary conditions, empty states, nulls

## Coverage Tools

### 1. VS Code Extensions

- **Coverage Gutters**: Shows coverage inline in editor
- **Jest Runner**: Run tests with coverage from editor
- **Test Explorer**: Visual test runner with coverage

### 2. CI Integration

Coverage is automatically checked in:
- Pull requests (must not decrease)
- Main branch pushes (updates badges)
- Nightly builds (full coverage report)

### 3. Coverage Badges

Badges are automatically generated:

![Backend Coverage](https://img.shields.io/badge/coverage-85%25-brightgreen)
![Frontend Coverage](https://img.shields.io/badge/coverage-80%25-green)
![Overall Coverage](https://img.shields.io/badge/coverage-83%25-green)

## Enforcement

### Pre-push Hook

Coverage is checked before pushing to protected branches:

```bash
# Bypass only in emergencies
git push --no-verify
```

### CI/CD Pipeline

Pull requests will fail if:
1. Coverage decreases from main branch
2. Coverage falls below thresholds
3. New code has <80% coverage

### Monitoring

Weekly reports show:
- Coverage trends
- Least covered files
- Recent coverage changes

## Best Practices

### 1. Write Tests First (TDD)

```typescript
// 1. Write failing test
it('calculates tax correctly', () => {
  expect(calculateTax(100, 0.08)).toBe(8);
});

// 2. Implement minimum code
function calculateTax(amount: number, rate: number): number {
  return amount * rate;
}

// 3. Refactor with confidence
```

### 2. Test Behavior, Not Implementation

```typescript
// ❌ Bad: Testing implementation details
expect(service._privateMethod()).toBe(true);
expect(component.state.isLoading).toBe(false);

// ✅ Good: Testing behavior
expect(service.getUser(1)).resolves.toEqual({ id: 1, name: 'Alice' });
expect(screen.getByText('Loading...')).not.toBeInTheDocument();
```

### 3. Meaningful Test Names

```typescript
// ❌ Bad
it('test 1', () => {});
it('works', () => {});

// ✅ Good
it('returns user data when ID exists', () => {});
it('throws NotFoundError when user ID does not exist', () => {});
```

### 4. Use Coverage as a Guide

- 100% coverage doesn't mean bug-free
- Focus on meaningful tests
- Some code might not need testing (types, constants)
- Exclude generated code from coverage

## Troubleshooting

### Coverage Not Updating

```bash
# Clear Jest cache
npm run test -- --clearCache

# Delete coverage data
rm -rf coverage/
rm -rf packages/*/coverage/

# Run with fresh cache
npm run test:coverage -- --no-cache
```

### False Positives

Some code might show as uncovered incorrectly:

1. **Async code**: Ensure promises are awaited
2. **Callbacks**: Mock and verify they're called
3. **Event handlers**: Trigger events in tests
4. **Default exports**: Import and test separately

### Performance Issues

Large test suites can be slow:

```bash
# Run in parallel
npm run test:coverage -- --maxWorkers=4

# Run only changed files
npm run test:coverage -- --onlyChanged

# Use focused coverage
npm run test:coverage -- --collectCoverageFrom='src/services/**'
```

## Resources

- [Jest Coverage Docs](https://jestjs.io/docs/configuration#coveragethreshold-object)
- [Istanbul Coverage Reports](https://istanbul.js.org/)
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Test Coverage in Practice](https://kentcdodds.com/blog/write-tests)