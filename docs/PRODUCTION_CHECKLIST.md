# Production Deployment Checklist

## Pre-deployment Checks

### 1. Code Quality
- [ ] Run TypeScript compilation: `npm run build`
- [ ] Fix all TypeScript errors in production code
- [ ] Run linter: `npm run lint`
- [ ] Run formatter: `npm run format`

### 2. Dependencies
- [ ] Check all imports use direct paths (not re-exports from index files)
- [ ] Verify all dependencies are in correct package.json
- [ ] No circular dependencies

### 3. Error Handling
- [ ] All catch blocks use correct parameter names
- [ ] No undefined variables in error handlers
- [ ] Proper error logging in place

### 4. Build Process
- [ ] Clean build works: `npm run clean && npm run build`
- [ ] Docker build works: `docker-compose build --no-cache`
- [ ] No build warnings about missing dependencies

### 5. Configuration
- [ ] Environment variables properly set in .env.production
- [ ] NODE_OPTIONS set for memory management
- [ ] Cache headers configured correctly in nginx

### 6. Testing
- [ ] Critical paths have tests
- [ ] API endpoints tested
- [ ] Authentication flow tested

## Deployment Steps

1. **Clean old builds**:
   ```bash
   ./scripts/docker-cleanup.sh
   ```

2. **Build and deploy**:
   ```bash
   ./scripts/clean-deploy.sh
   ```

3. **Verify services**:
   ```bash
   docker-compose --profile prod ps
   curl https://spherosegapp.utia.cas.cz/api/health
   ```

4. **Monitor logs**:
   ```bash
   docker-compose --profile prod logs -f
   ```

## Common Issues and Solutions

### Browser shows old cached files
- Solution: Configure nginx with shorter cache times for JS/CSS
- Add cache-busting headers in index.html

### "error is not defined" errors
- Solution: Check all catch blocks use the correct parameter name
- Enable TypeScript strict mode

### Import errors in production
- Solution: Use direct imports instead of barrel exports
- Example: `import { something } from '@shared/utils/file'` not `from '@shared'`

### Memory issues
- Solution: Set NODE_OPTIONS in docker-compose.yml
- Example: `NODE_OPTIONS=--max-old-space-size=768`

### React initialization errors
- Solution: Ensure React is loaded before any components
- Check Vite chunk configuration

## Monitoring

- Check health endpoint regularly: `/api/health`
- Monitor Docker logs for errors
- Set up alerts for high memory usage
- Track API response times