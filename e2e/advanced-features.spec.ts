import { test, expect } from '@playwright/test';

// Advanced Features E2E Tests
test.describe('Advanced Features', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/signin');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testuser123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  // Batch Operations Tests
  test.describe('Batch Operations', () => {
    test('should handle batch image upload', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Click batch upload
      await page.click('button:has-text("Upload Images")');
      
      // Select multiple files
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('[data-testid="choose-files"]')
      ]);
      
      // Create multiple test files
      const files = [];
      for (let i = 1; i <= 10; i++) {
        files.push({
          name: `batch-image-${i}.jpg`,
          mimeType: 'image/jpeg',
          buffer: Buffer.from(`fake-image-data-${i}`)
        });
      }
      
      await fileChooser.setFiles(files);
      
      // Should show batch upload progress
      await expect(page.locator('[data-testid="batch-upload-progress"]')).toBeVisible();
      await expect(page.locator('text=Uploading 10 files')).toBeVisible();
      
      // Should show individual progress
      await expect(page.locator('[data-testid="file-progress"]')).toHaveCount(10);
      
      // Wait for completion
      await expect(page.locator('text=All files uploaded successfully')).toBeVisible({ timeout: 30000 });
      
      // Verify all images appear
      await expect(page.locator('[data-testid="image-card"]')).toHaveCount(10);
    });

    test('should handle batch segmentation', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Select multiple images
      await page.click('[data-testid="select-mode-toggle"]');
      
      // Select first 5 images
      const images = page.locator('[data-testid="image-checkbox"]');
      for (let i = 0; i < 5; i++) {
        await images.nth(i).click();
      }
      
      // Should show selection count
      await expect(page.locator('text=5 images selected')).toBeVisible();
      
      // Start batch segmentation
      await page.click('button:has-text("Segment Selected")');
      
      // Configure batch settings
      await expect(page.locator('text=Batch Segmentation Settings')).toBeVisible();
      await page.selectOption('[name="priority"]', 'high');
      await page.check('[name="notifyOnComplete"]');
      
      // Start processing
      await page.click('button:has-text("Start Batch Segmentation")');
      
      // Should show batch progress
      await expect(page.locator('[data-testid="batch-progress"]')).toBeVisible();
      await expect(page.locator('text=Processing 5 images')).toBeVisible();
      
      // Should update status for each image
      await expect(page.locator('[data-status="queued"]')).toHaveCount(5);
    });

    test('should handle batch export', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Select images for export
      await page.click('[data-testid="select-mode-toggle"]');
      await page.click('[data-testid="select-all"]');
      
      // Open batch actions
      await page.click('button:has-text("Batch Actions")');
      await page.click('text=Export Selected');
      
      // Configure export
      await expect(page.locator('text=Export Configuration')).toBeVisible();
      await page.selectOption('[name="format"]', 'excel');
      await page.check('[name="includeMetadata"]');
      await page.check('[name="includeSegmentations"]');
      await page.check('[name="includeThumbnails"]');
      
      // Start export
      await page.click('button:has-text("Export")');
      
      // Should show progress
      await expect(page.locator('[data-testid="export-progress"]')).toBeVisible();
      
      // Wait for download
      const download = await page.waitForEvent('download');
      expect(download.suggestedFilename()).toMatch(/batch-export.*\.xlsx/);
    });

    test('should handle batch deletion', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Select images for deletion
      await page.click('[data-testid="select-mode-toggle"]');
      
      // Select specific images
      await page.click('[data-testid="image-checkbox"]:first-child');
      await page.click('[data-testid="image-checkbox"]:nth-child(3)');
      await page.click('[data-testid="image-checkbox"]:nth-child(5)');
      
      // Batch delete
      await page.click('button:has-text("Batch Actions")');
      await page.click('text=Delete Selected');
      
      // Confirm deletion
      await expect(page.locator('text=Delete 3 images?')).toBeVisible();
      await expect(page.locator('text=This action cannot be undone')).toBeVisible();
      
      await page.click('button:has-text("Delete Images")');
      
      // Should show success
      await expect(page.locator('text=3 images deleted successfully')).toBeVisible();
      
      // Images should be removed from view
      const remainingImages = await page.locator('[data-testid="image-card"]').count();
      expect(remainingImages).toBeLessThan(10);
    });

    test('should handle batch metadata update', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Select images
      await page.click('[data-testid="select-mode-toggle"]');
      await page.click('[data-testid="select-all"]');
      
      // Open batch metadata editor
      await page.click('button:has-text("Batch Actions")');
      await page.click('text=Edit Metadata');
      
      // Update metadata
      await expect(page.locator('text=Batch Metadata Editor')).toBeVisible();
      await page.fill('[name="tags"]', 'batch-test, automated, sample');
      await page.selectOption('[name="category"]', 'research');
      await page.fill('[name="notes"]', 'Batch metadata update test');
      
      // Apply to all
      await page.click('button:has-text("Apply to All Selected")');
      
      // Should show confirmation
      await expect(page.locator('text=Update metadata for')).toBeVisible();
      await page.click('button:has-text("Confirm Update")');
      
      // Verify update
      await expect(page.locator('text=Metadata updated successfully')).toBeVisible();
      
      // Check individual image has new metadata
      await page.click('[data-testid="image-card"]:first-child');
      await expect(page.locator('text=batch-test')).toBeVisible();
    });
  });

  // Advanced Search Tests
  test.describe('Advanced Search', () => {
    test('should support complex search queries', async ({ page }) => {
      await page.goto('/projects');
      
      // Open advanced search
      await page.click('button:has-text("Advanced Search")');
      
      // Build complex query
      await page.fill('[name="projectName"]', 'test');
      await page.selectOption('[name="dateRange"]', 'last30days');
      await page.fill('[name="minImages"]', '5');
      await page.fill('[name="tags"]', 'research, biology');
      await page.check('[name="hasSegmentations"]');
      await page.selectOption('[name="status"]', 'active');
      
      // Execute search
      await page.click('button:has-text("Search")');
      
      // Should show filtered results
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
      await expect(page.locator('[data-testid="result-count"]')).toBeVisible();
      
      // Results should match criteria
      const results = page.locator('.project-card');
      const count = await results.count();
      
      if (count > 0) {
        // Verify first result matches
        const firstResult = results.first();
        await expect(firstResult).toContainText('test');
      }
    });

    test('should support search filters and facets', async ({ page }) => {
      await page.goto('/projects');
      
      // Use faceted search
      await page.fill('[data-testid="search-input"]', 'cell');
      await page.press('[data-testid="search-input"]', 'Enter');
      
      // Should show facets
      await expect(page.locator('[data-testid="search-facets"]')).toBeVisible();
      await expect(page.locator('text=Filter by:')).toBeVisible();
      
      // Apply filters
      await page.click('[data-testid="facet-date-last-week"]');
      await page.click('[data-testid="facet-type-images"]');
      await page.click('[data-testid="facet-status-completed"]');
      
      // Results should update
      await expect(page.locator('[data-testid="active-filters"]')).toBeVisible();
      await expect(page.locator('text=Last week')).toBeVisible();
      await expect(page.locator('text=Images')).toBeVisible();
      await expect(page.locator('text=Completed')).toBeVisible();
    });

    test('should support saved searches', async ({ page }) => {
      await page.goto('/projects');
      
      // Create a search
      await page.click('button:has-text("Advanced Search")');
      await page.fill('[name="projectName"]', 'important');
      await page.selectOption('[name="dateRange"]', 'last7days');
      await page.check('[name="hasSegmentations"]');
      
      // Save search
      await page.click('button:has-text("Save Search")');
      await page.fill('[name="searchName"]', 'Recent Important Projects');
      await page.click('button:has-text("Save")');
      
      // Should appear in saved searches
      await page.click('[data-testid="saved-searches"]');
      await expect(page.locator('text=Recent Important Projects')).toBeVisible();
      
      // Should be executable
      await page.click('text=Recent Important Projects');
      await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    });

    test('should support search history', async ({ page }) => {
      await page.goto('/projects');
      
      // Perform several searches
      const searches = ['test', 'cell', 'research', 'sample'];
      
      for (const term of searches) {
        await page.fill('[data-testid="search-input"]', term);
        await page.press('[data-testid="search-input"]', 'Enter');
        await page.waitForTimeout(500);
      }
      
      // Open search history
      await page.click('[data-testid="search-input"]');
      await expect(page.locator('[data-testid="search-history"]')).toBeVisible();
      
      // Should show recent searches
      for (const term of searches.reverse()) {
        await expect(page.locator(`[data-testid="history-item-${term}"]`)).toBeVisible();
      }
      
      // Should be clickable
      await page.click('[data-testid="history-item-test"]');
      await expect(page.locator('[data-testid="search-input"]')).toHaveValue('test');
    });
  });

  // Advanced Export Features Tests
  test.describe('Advanced Export Features', () => {
    test('should support custom export templates', async ({ page }) => {
      await page.click('.project-card:first-child');
      await page.click('button:has-text("Export")');
      
      // Choose custom template
      await page.click('text=Custom Export');
      
      // Configure template
      await expect(page.locator('text=Export Template Builder')).toBeVisible();
      
      // Select fields
      await page.check('[name="field-imageName"]');
      await page.check('[name="field-uploadDate"]');
      await page.check('[name="field-segmentationCount"]');
      await page.check('[name="field-cellArea"]');
      await page.check('[name="field-cellPerimeter"]');
      
      // Configure formatting
      await page.selectOption('[name="dateFormat"]', 'ISO8601');
      await page.selectOption('[name="numberFormat"]', '2-decimal');
      
      // Save template
      await page.click('button:has-text("Save Template")');
      await page.fill('[name="templateName"]', 'Cell Analysis Export');
      await page.click('button:has-text("Save")');
      
      // Export with template
      await page.click('button:has-text("Export with Template")');
      
      const download = await page.waitForEvent('download');
      expect(download.suggestedFilename()).toContain('cell-analysis');
    });

    test('should support multiple export formats', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Test each export format
      const formats = [
        { format: 'excel', extension: 'xlsx' },
        { format: 'csv', extension: 'csv' },
        { format: 'json', extension: 'json' },
        { format: 'xml', extension: 'xml' },
        { format: 'matlab', extension: 'mat' }
      ];
      
      for (const { format, extension } of formats) {
        await page.click('button:has-text("Export")');
        await page.click(`text=Export as ${format.toUpperCase()}`);
        
        const download = await page.waitForEvent('download');
        expect(download.suggestedFilename()).toContain(`.${extension}`);
      }
    });

    test('should support scheduled exports', async ({ page }) => {
      await page.click('.project-card:first-child');
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Automated Exports');
      
      // Configure scheduled export
      await expect(page.locator('text=Schedule Automatic Exports')).toBeVisible();
      
      await page.check('[name="enableScheduledExport"]');
      await page.selectOption('[name="exportFrequency"]', 'weekly');
      await page.selectOption('[name="exportDay"]', 'monday');
      await page.fill('[name="exportTime"]', '09:00');
      await page.selectOption('[name="exportFormat"]', 'excel');
      await page.fill('[name="exportEmail"]', '<EMAIL>');
      
      // Save schedule
      await page.click('button:has-text("Save Schedule")');
      
      await expect(page.locator('text=Export schedule saved')).toBeVisible();
      await expect(page.locator('text=Next export: Monday at 9:00 AM')).toBeVisible();
    });

    test('should generate comprehensive reports', async ({ page }) => {
      await page.click('.project-card:first-child');
      await page.click('button:has-text("Generate Report")');
      
      // Configure report
      await expect(page.locator('text=Report Configuration')).toBeVisible();
      
      // Select report sections
      await page.check('[name="includeExecutiveSummary"]');
      await page.check('[name="includeMethodology"]');
      await page.check('[name="includeDetailedResults"]');
      await page.check('[name="includeStatistics"]');
      await page.check('[name="includeVisualizations"]');
      await page.check('[name="includeAppendix"]');
      
      // Configure visualizations
      await page.check('[name="chartHistogram"]');
      await page.check('[name="chartScatterplot"]');
      await page.check('[name="chartHeatmap"]');
      
      // Generate report
      await page.click('button:has-text("Generate Report")');
      
      // Should show generation progress
      await expect(page.locator('[data-testid="report-progress"]')).toBeVisible();
      await expect(page.locator('text=Generating visualizations')).toBeVisible();
      await expect(page.locator('text=Compiling report')).toBeVisible();
      
      // Download report
      const download = await page.waitForEvent('download');
      expect(download.suggestedFilename()).toMatch(/report.*\.pdf/);
    });
  });

  // Keyboard Shortcuts Tests
  test.describe('Keyboard Shortcuts', () => {
    test('should support global keyboard shortcuts', async ({ page }) => {
      // Test search shortcut
      await page.keyboard.press('Control+K');
      await expect(page.locator('[data-testid="global-search"]')).toBeFocused();
      await page.keyboard.press('Escape');
      
      // Test navigation shortcuts
      await page.keyboard.press('g p'); // Go to projects
      await expect(page).toHaveURL(/.*projects/);
      
      await page.keyboard.press('g d'); // Go to dashboard
      await expect(page).toHaveURL(/.*dashboard/);
      
      await page.keyboard.press('g s'); // Go to settings
      await expect(page).toHaveURL(/.*settings/);
    });

    test('should support context-specific shortcuts', async ({ page }) => {
      await page.click('.project-card:first-child');
      
      // Image gallery shortcuts
      await page.keyboard.press('a'); // Select all
      await expect(page.locator('[data-testid="selection-count"]')).toBeVisible();
      
      await page.keyboard.press('Escape'); // Clear selection
      await expect(page.locator('[data-testid="selection-count"]')).not.toBeVisible();
      
      // Upload shortcut
      await page.keyboard.press('u');
      await expect(page.locator('[data-testid="upload-dialog"]')).toBeVisible();
      await page.keyboard.press('Escape');
    });

    test('should show keyboard shortcuts help', async ({ page }) => {
      // Open shortcuts help
      await page.keyboard.press('?');
      
      // Should show shortcuts modal
      await expect(page.locator('text=Keyboard Shortcuts')).toBeVisible();
      await expect(page.locator('[data-testid="shortcuts-list"]')).toBeVisible();
      
      // Should be categorized
      await expect(page.locator('text=Global Shortcuts')).toBeVisible();
      await expect(page.locator('text=Navigation')).toBeVisible();
      await expect(page.locator('text=Actions')).toBeVisible();
      
      // Close help
      await page.keyboard.press('Escape');
      await expect(page.locator('text=Keyboard Shortcuts')).not.toBeVisible();
    });
  });

  // Advanced Filtering and Sorting Tests
  test.describe('Advanced Filtering and Sorting', () => {
    test('should support multi-criteria filtering', async ({ page }) => {
      await page.goto('/projects');
      
      // Open filter panel
      await page.click('button:has-text("Filters")');
      
      // Apply multiple filters
      await page.selectOption('[name="filterStatus"]', 'active');
      await page.fill('[name="filterMinImages"]', '10');
      await page.fill('[name="filterMaxImages"]', '100');
      await page.selectOption('[name="filterDateCreated"]', 'lastMonth');
      await page.fill('[name="filterTags"]', 'research');
      await page.check('[name="filterHasSegmentations"]');
      
      // Apply filters
      await page.click('button:has-text("Apply Filters")');
      
      // Should show filtered results
      await expect(page.locator('[data-testid="filter-summary"]')).toBeVisible();
      await expect(page.locator('text=6 filters applied')).toBeVisible();
    });

    test('should support custom sorting', async ({ page }) => {
      await page.goto('/projects');
      
      // Open sort options
      await page.click('[data-testid="sort-dropdown"]');
      
      // Test different sort options
      const sortOptions = [
        { value: 'name-asc', text: 'Name (A-Z)' },
        { value: 'name-desc', text: 'Name (Z-A)' },
        { value: 'created-desc', text: 'Newest First' },
        { value: 'created-asc', text: 'Oldest First' },
        { value: 'images-desc', text: 'Most Images' },
        { value: 'images-asc', text: 'Least Images' }
      ];
      
      for (const option of sortOptions) {
        await page.selectOption('[name="sortBy"]', option.value);
        await expect(page.locator(`text=Sorted by: ${option.text}`)).toBeVisible();
        await page.waitForTimeout(500); // Wait for re-sort
      }
    });

    test('should persist filter preferences', async ({ page }) => {
      await page.goto('/projects');
      
      // Set filters
      await page.click('button:has-text("Filters")');
      await page.selectOption('[name="filterStatus"]', 'active');
      await page.fill('[name="filterTags"]', 'important');
      await page.click('button:has-text("Apply Filters")');
      
      // Save as default
      await page.click('button:has-text("Save as Default")');
      
      // Navigate away and back
      await page.goto('/dashboard');
      await page.goto('/projects');
      
      // Filters should be restored
      await expect(page.locator('[data-testid="filter-summary"]')).toBeVisible();
      await expect(page.locator('text=Default filters applied')).toBeVisible();
    });
  });
});