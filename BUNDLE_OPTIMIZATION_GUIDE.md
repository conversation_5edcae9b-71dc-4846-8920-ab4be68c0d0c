# Frontend Bundle Optimization Guide

This guide provides comprehensive strategies for optimizing the frontend bundle size and performance in SpherosegV4.

## Current Bundle Structure

The application uses Vite with the following chunking strategy:

### Core Chunks
- **react-core**: React, ReactDOM, and core React libraries (~40KB gzipped)
- **react-ui**: UI component libraries (Radix UI, Lucide, etc.) (~60KB gzipped)
- **data-layer**: Data fetching and state management (~30KB gzipped)

### Feature Chunks
- **auth**: Authentication pages and services (~15KB gzipped)
- **project**: Project management features (~25KB gzipped)
- **segmentation**: Main segmentation canvas and tools (~80KB gzipped)
- **export**: Export functionality (~20KB gzipped)
- **dashboard**: Dashboard and analytics (~35KB gzipped)

## Optimization Strategies

### 1. Code Splitting

#### Route-Based Splitting
```typescript
// Use dynamic imports for routes
const Dashboard = lazy(() => import('./pages/Dashboard'));
const ProjectDetail = lazy(() => import('./pages/ProjectDetail'));
const SegmentationPage = lazy(() => import('./pages/segmentation/SegmentationPage'));
```

#### Component-Based Splitting
```typescript
// Split heavy components
const Canvas = lazy(() => import('./components/Canvas'));
const ExcelExporter = lazy(() => import('./components/ExcelExporter'));
const AnalyticsDashboard = lazy(() => import('./components/AnalyticsDashboard'));
```

### 2. Tree Shaking

#### Import Specific Functions
```typescript
// ❌ Bad - imports entire library
import * as polygonUtils from '@spheroseg/shared';

// ✅ Good - imports only needed functions
import { calculateArea, simplifyPolygon } from '@spheroseg/shared';
```

#### Use ES Modules
```typescript
// Ensure all dependencies support ES modules for better tree shaking
// Check package.json for "module" field
```

### 3. Bundle Analysis

#### Run Analysis
```bash
# Build with analysis
npm run build:analyze

# Or manually analyze existing build
npm run analyze:bundle
```

#### Interpret Results
- **Red chunks**: Over 500KB - need immediate attention
- **Yellow chunks**: 200-500KB - consider splitting
- **Green chunks**: Under 200KB - optimal size

### 4. Dependency Optimization

#### Replace Heavy Dependencies
```typescript
// Replace moment.js (67KB) with date-fns (12KB for used functions)
// ❌ import moment from 'moment';
// ✅ import { format, parseISO } from 'date-fns';

// Replace lodash (71KB) with specific functions
// ❌ import _ from 'lodash';
// ✅ import debounce from 'lodash/debounce';
```

#### Use Modern Alternatives
- **Icons**: Use lucide-react instead of react-icons
- **Forms**: Use react-hook-form instead of formik
- **Styling**: Use CSS modules or Tailwind instead of styled-components

### 5. Asset Optimization

#### Image Optimization
```typescript
// Use next-gen formats
<picture>
  <source srcSet="image.webp" type="image/webp" />
  <source srcSet="image.jpg" type="image/jpeg" />
  <img src="image.jpg" alt="Description" />
</picture>
```

#### Font Optimization
```css
/* Preload critical fonts */
<link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>

/* Use font-display: swap */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  src: url('/fonts/inter-var.woff2') format('woff2');
}
```

### 6. Performance Budgets

Set and enforce bundle size limits:

```javascript
// vite.config.ts
build: {
  chunkSizeWarningLimit: 200, // 200KB warning threshold
  rollupOptions: {
    output: {
      manualChunks: {
        // Enforce maximum chunk sizes
        vendor: {
          test: /node_modules/,
          maxSize: 500000, // 500KB max
        },
      },
    },
  },
}
```

## Advanced Optimizations

### 1. Progressive Enhancement

```typescript
// Load features progressively based on user interaction
const loadAdvancedFeatures = async () => {
  const [
    { AdvancedCanvas },
    { WebGLRenderer },
    { CollaborationTools },
  ] = await Promise.all([
    import('./features/AdvancedCanvas'),
    import('./features/WebGLRenderer'),
    import('./features/CollaborationTools'),
  ]);
  
  // Initialize features
};
```

### 2. Resource Hints

```html
<!-- Preconnect to API -->
<link rel="preconnect" href="https://api.spheroseg.com">

<!-- Prefetch next likely navigation -->
<link rel="prefetch" href="/assets/js/project-chunk.js">

<!-- Preload critical resources -->
<link rel="preload" href="/assets/js/react-core.js" as="script">
```

### 3. Service Worker Caching

```javascript
// Cache immutable assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open('v1').then((cache) => {
      return cache.addAll([
        '/assets/js/react-core-[hash].js',
        '/assets/js/react-ui-[hash].js',
        '/assets/css/main-[hash].css',
      ]);
    })
  );
});
```

## Monitoring and Maintenance

### 1. Continuous Monitoring

```json
// package.json scripts
{
  "scripts": {
    "build:analyze": "ANALYZE=true npm run build",
    "bundle:check": "bundlesize",
    "lighthouse": "lighthouse https://spheroseg.com --output html"
  }
}
```

### 2. Bundle Size Checks

```json
// bundlesize.config.json
{
  "files": [
    {
      "path": "./dist/assets/js/react-core-*.js",
      "maxSize": "50 KB"
    },
    {
      "path": "./dist/assets/js/main-*.js",
      "maxSize": "100 KB"
    },
    {
      "path": "./dist/assets/js/vendor-*.js",
      "maxSize": "200 KB"
    }
  ]
}
```

### 3. Performance Budget Enforcement

```yaml
# .github/workflows/bundle-check.yml
name: Bundle Size Check
on: [pull_request]
jobs:
  size:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm run build
      - run: npm run bundle:check
```

## Optimization Checklist

### Before Release
- [ ] Run bundle analysis (`npm run build:analyze`)
- [ ] Check for duplicate dependencies
- [ ] Verify lazy loading is working
- [ ] Test on slow 3G network
- [ ] Check Core Web Vitals scores
- [ ] Review chunk sizes against budgets

### Regular Maintenance
- [ ] Monthly dependency audit
- [ ] Quarterly bundle size review
- [ ] Update to latest optimized versions
- [ ] Remove unused dependencies
- [ ] Review and update chunk strategy

## Quick Wins

1. **Enable Compression**: Ensure Gzip/Brotli is enabled on server
2. **Update Dependencies**: Use latest versions with better tree shaking
3. **Remove Unused Code**: Delete dead code and unused exports
4. **Optimize Images**: Convert to WebP, use responsive images
5. **Lazy Load Routes**: Split each route into its own chunk
6. **Cache Static Assets**: Set long cache headers for hashed assets

## Troubleshooting

### Large Bundle Size
1. Run `npm run analyze:bundle`
2. Identify largest chunks
3. Look for duplicate libraries
4. Check for accidental inclusion of dev dependencies
5. Verify tree shaking is working

### Slow Initial Load
1. Check network waterfall in DevTools
2. Verify critical resources are preloaded
3. Ensure compression is enabled
4. Check for render-blocking resources
5. Implement progressive enhancement

### Memory Issues
1. Profile memory usage in DevTools
2. Look for memory leaks in components
3. Implement virtualization for large lists
4. Lazy load heavy components
5. Clean up event listeners and subscriptions

## Resources

- [Vite Performance Guide](https://vitejs.dev/guide/performance.html)
- [Web.dev Performance](https://web.dev/performance/)
- [Bundle Phobia](https://bundlephobia.com/) - Check package sizes
- [Webpack Bundle Analyzer](https://github.com/webpack-contrib/webpack-bundle-analyzer)
- [Source Map Explorer](https://github.com/danvk/source-map-explorer)