{"name": "spheroseg", "version": "1.0.0", "description": "SpheroSeg - Cell Segmentation Application", "private": true, "packageManager": "npm@10.5.0", "type": "module", "workspaces": ["packages/*"], "dependencies": {}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.25.1", "@playwright/test": "^1.54.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.4.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-config-recommended": "^4.1.0", "eslint-plugin-react": "^7.37.5", "express": "^5.1.0", "glob": "^11.0.3", "globals": "^16.0.0", "happy-dom": "^17.4.6", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "jscpd": "^4.0.0", "jsdom": "^26.1.0", "markdownlint-cli": "^0.45.0", "playwright": "^1.54.1", "prettier": "^3.2.5", "turbo": "^2.5.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.4"}, "scripts": {"dev": "turbo run dev", "build": "turbo run build", "preview": "turbo run preview", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "turbo run format", "format:check": "turbo run format:check", "test": "turbo run test", "test:coverage": "turbo run test:coverage", "test:ci": "turbo run test:ci", "coverage:check": "node scripts/coverage-check.js", "coverage:report": "npm run test:coverage && npm run coverage:check", "coverage:view": "open coverage/index.html", "test:integration": "vitest run --config vitest.integration.config.ts", "code:check": "turbo run code:check", "code:fix": "turbo run code:fix", "clean": "turbo run clean", "dev:frontend": "turbo run dev --filter=frontend", "dev:backend": "turbo run dev --filter=backend", "test:frontend": "turbo run test --filter=@spheroseg/frontend", "test:backend": "turbo run test --filter=backend", "test:ml": "turbo run test --filter=ml", "ml:segmentation": "turbo run segmentation --filter=ml", "ml:extract": "turbo run extract --filter=ml", "e2e": "playwright test", "e2e:open": "playwright test --ui", "e2e:headed": "playwright test --headed", "e2e:debug": "playwright test --debug", "e2e:coverage": "COLLECT_COVERAGE=true playwright test && npm run e2e:coverage:report", "e2e:coverage:report": "tsx scripts/e2e-coverage-report.ts test-results/results.json coverage-report", "e2e:coverage:open": "open coverage-report/index.html", "e2e:security": "playwright test security.spec.ts", "e2e:profile": "playwright test user-profile.spec.ts", "e2e:integration": "playwright test integration.spec.ts", "e2e:resilience": "playwright test resilience.spec.ts", "e2e:gdpr": "playwright test data-management.spec.ts", "e2e:features": "playwright test advanced-features.spec.ts", "e2e:collaboration": "playwright test collaboration.spec.ts", "e2e:monitoring": "bash scripts/run-monitoring-e2e-tests.sh", "duplicates": "npx jscpd . --config ./.jscpd.json", "init:db": "node scripts/init-db.js", "init:db:docker": "node scripts/init-db-docker.js", "db:migrate": "cd packages/backend && npm run migrate:up", "db:create-test-user": "node scripts/create-test-user.js", "test:validation-scripts": "node scripts/test-validation-scripts.js", "validate:imports": "node scripts/validate-imports.js", "test:backup-recovery": "bash scripts/test-backup-recovery.sh", "backup:database": "bash scripts/backup/backup-database.sh", "restore:database": "bash scripts/rollback/restore-database.sh"}, "engines": {"node": ">=18.0.0"}}