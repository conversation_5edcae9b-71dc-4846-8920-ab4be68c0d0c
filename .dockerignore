# Node modules
node_modules
**/node_modules
packages/*/node_modules

# Build outputs
dist
**/dist
build
**/build
.turbo
**/.turbo

# Test coverage
coverage
**/coverage
.nyc_output

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Editor directories
.vscode
.idea

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
.dockerignore
Dockerfile*
docker-compose*.yml

# Environment files
.env
.env.*
!.env.example

# Test results
test-results
playwright-report
e2e-results

# Cache
.eslintcache
.stylelintcache
*.cache

# Temporary files
*.tmp
*.temp
.tmp
.temp

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv
pip-log.txt
pip-delete-this-directory.txt

# ML model files
*.pt
*.pth
*.onnx
*.h5
*.pkl
*.joblib
checkpoints/
models/

# Large data files
*.csv
!package.json
!package-lock.json
!tsconfig.json
!turbo.json
*.sql
*.db
*.sqlite

# Backup files
*.backup
*.bak
*.swp
*.swo

# Documentation
docs/
*.md
!README.md
!CLAUDE.md
!package.json
!package-lock.json

# Scripts (except essential ones)
scripts/
!scripts/init-db.js
!scripts/init-db-docker.js

# Other unnecessary files
.turbo
.github
.husky
playwright.config.ts
vitest.config.ts
jest.config.js
tsconfig.tsbuildinfo