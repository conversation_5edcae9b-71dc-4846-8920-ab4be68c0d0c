/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts?(x)', '**/?(*.)+(spec|test).ts?(x)'],
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: {
        esModuleInterop: true,
        allowJs: true,
      },
    }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@spheroseg/types$': '<rootDir>/../types/src',
    '^@spheroseg/shared/src/(.*)$': '<rootDir>/../shared/src/$1',
    '^@spheroseg/shared/(.*)$': '<rootDir>/../shared/src/$1',
    '^@spheroseg/shared$': '<rootDir>/../shared/src',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(@spheroseg|node-fetch)/)',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/types/**',
    '!src/migrations/**',
    '!src/__tests__/setup.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'json', 'json-summary', 'html'],
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 80,
      lines: 85,
      statements: 85,
    },
  },
  testPathIgnorePatterns: ['/node_modules/', '/dist/', '.*setup\\.ts$', 'src/routes/test\\.ts$'],
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],
  // Memory optimization - aggressive settings
  // maxWorkers: 1, // Removed to avoid conflict with --runInBand in test:memory script
  workerIdleMemoryLimit: '512MB',
  // Timeout configuration
  testTimeout: 10000,
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  resetModules: true,
  // Force garbage collection and memory cleanup
  forceExit: true,
  detectOpenHandles: false,
  // Aggressive garbage collection
  logHeapUsage: true,
  // Limit test files run in parallel - ultra conservative
  maxConcurrency: 1,
  // Worker configuration for memory optimization
  maxWorkers: 1,
  // Handle ESM modules like node-fetch
  transformIgnorePatterns: [
    'node_modules/(?!(node-fetch|fetch-blob|data-uri-to-buffer|formdata-polyfill)/)',
  ],
  // Cache directory for memory optimization
  cacheDirectory: '<rootDir>/node_modules/.cache/jest',
  // ESM support
  extensionsToTreatAsEsm: ['.ts'],
  globals: {
    'ts-jest': {
      useESM: false,
      isolatedModules: true,
    },
  },
};