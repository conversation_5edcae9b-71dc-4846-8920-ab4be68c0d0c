/**
 * Type Safety Helpers
 * 
 * Utilities to improve type safety across the codebase by providing
 * properly typed alternatives to common patterns that use 'any'
 */

/**
 * Strictly typed function wrapper that preserves parameter and return types
 */
export type AsyncFunction<TParams extends readonly unknown[], TReturn> = (
  ...args: TParams
) => Promise<TReturn>;

export type SyncFunction<TParams extends readonly unknown[], TReturn> = (
  ...args: TParams
) => TReturn;

/**
 * Type-safe error handler wrapper
 */
export function createTypedErrorHandler<T extends AsyncFunction<any[], any>>(
  serviceName: string,
  methodName: string,
  fn: T
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      return await fn(...args);
    } catch (error) {
      // Log error with context
      console.error(`[${serviceName}.${methodName}] Error:`, error);
      throw error;
    }
  }) as T;
}

/**
 * Type-safe query result wrapper for database operations
 */
export interface TypedQueryResult<T> {
  rows: T[];
  rowCount: number;
  command: string;
  oid: number;
  fields: Array<{
    name: string;
    tableID: number;
    columnID: number;
    dataTypeID: number;
    dataTypeSize: number;
    dataTypeModifier: number;
    format: string;
  }>;
}

/**
 * Type-safe database query function
 */
export type TypedQuery = <T = unknown>(
  text: string,
  values?: unknown[]
) => Promise<TypedQueryResult<T>>;

/**
 * Type guard to check if a value is defined
 */
export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null;
}

/**
 * Type guard for error objects
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Type-safe object keys helper
 */
export function typedKeys<T extends object>(obj: T): Array<keyof T> {
  return Object.keys(obj) as Array<keyof T>;
}

/**
 * Type-safe object entries helper
 */
export function typedEntries<T extends object>(
  obj: T
): Array<[keyof T, T[keyof T]]> {
  return Object.entries(obj) as Array<[keyof T, T[keyof T]]>;
}

/**
 * Type-safe pick function
 */
export function typedPick<T extends object, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach((key) => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

/**
 * Type-safe omit function
 */
export function typedOmit<T extends object, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj };
  keys.forEach((key) => {
    delete result[key];
  });
  return result as Omit<T, K>;
}

/**
 * Type-safe partial update function
 */
export function typedUpdate<T extends object>(
  target: T,
  updates: Partial<T>
): T {
  return { ...target, ...updates };
}

/**
 * Type-safe array filter that removes undefined/null values
 */
export function filterDefined<T>(
  array: Array<T | undefined | null>
): T[] {
  return array.filter(isDefined);
}

/**
 * Type-safe promise all settled result handler
 */
export type SettledResult<T> =
  | { status: 'fulfilled'; value: T }
  | { status: 'rejected'; reason: unknown };

export function handleSettledResults<T>(
  results: SettledResult<T>[]
): {
  fulfilled: T[];
  rejected: unknown[];
} {
  const fulfilled: T[] = [];
  const rejected: unknown[] = [];

  results.forEach((result) => {
    if (result.status === 'fulfilled') {
      fulfilled.push(result.value);
    } else {
      rejected.push(result.reason);
    }
  });

  return { fulfilled, rejected };
}

/**
 * Type-safe event emitter types
 */
export interface TypedEventMap {
  [event: string]: unknown;
}

export interface TypedEventEmitter<TEvents extends TypedEventMap> {
  on<K extends keyof TEvents>(
    event: K,
    listener: (data: TEvents[K]) => void
  ): void;
  
  off<K extends keyof TEvents>(
    event: K,
    listener: (data: TEvents[K]) => void
  ): void;
  
  emit<K extends keyof TEvents>(event: K, data: TEvents[K]): void;
}

/**
 * Type-safe timeout wrapper
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutError = new Error('Operation timed out')
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(timeoutError), timeoutMs)
    ),
  ]);
}

/**
 * Type-safe retry wrapper
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: {
    maxAttempts?: number;
    delay?: number;
    backoff?: number;
    shouldRetry?: (error: unknown) => boolean;
  } = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoff = 2,
    shouldRetry = () => true,
  } = options;

  let lastError: unknown;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts || !shouldRetry(error)) {
        throw error;
      }

      const waitTime = delay * Math.pow(backoff, attempt - 1);
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
}