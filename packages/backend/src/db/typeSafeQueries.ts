/**
 * Type-Safe Database Query Helpers
 * 
 * Provides strongly typed database query functions and result handlers
 * to eliminate 'any' types in database operations
 */

import { QueryResult } from 'pg';
import db from './index';
import { TypedQueryResult } from '../utils/typeSafetyHelpers';

/**
 * Convert pg QueryResult to our typed version
 */
function toTypedResult<T>(result: QueryResult): TypedQueryResult<T> {
  return {
    rows: result.rows as T[],
    rowCount: result.rowCount || 0,
    command: result.command,
    oid: result.oid,
    fields: result.fields,
  };
}

/**
 * Type-safe query execution
 */
export async function query<T = unknown>(
  text: string,
  values?: unknown[]
): Promise<TypedQueryResult<T>> {
  const result = await db.query(text, values);
  return toTypedResult<T>(result);
}

/**
 * Type-safe single row query
 */
export async function queryOne<T = unknown>(
  text: string,
  values?: unknown[]
): Promise<T | null> {
  const result = await query<T>(text, values);
  return result.rows[0] || null;
}

/**
 * Type-safe query that must return exactly one row
 */
export async function queryOneRequired<T = unknown>(
  text: string,
  values?: unknown[],
  errorMessage = 'Expected exactly one row'
): Promise<T> {
  const result = await query<T>(text, values);
  if (result.rowCount !== 1) {
    throw new Error(`${errorMessage}: got ${result.rowCount} rows`);
  }
  return result.rows[0];
}

/**
 * Type-safe query for multiple rows
 */
export async function queryMany<T = unknown>(
  text: string,
  values?: unknown[]
): Promise<T[]> {
  const result = await query<T>(text, values);
  return result.rows;
}

/**
 * Type-safe exists query
 */
export async function exists(
  text: string,
  values?: unknown[]
): Promise<boolean> {
  const result = await query<{ exists: boolean }>(
    `SELECT EXISTS(${text}) as exists`,
    values
  );
  return result.rows[0]?.exists || false;
}

/**
 * Type-safe count query
 */
export async function count(
  table: string,
  where?: string,
  values?: unknown[]
): Promise<number> {
  const whereClause = where ? `WHERE ${where}` : '';
  const result = await query<{ count: string }>(
    `SELECT COUNT(*) as count FROM ${table} ${whereClause}`,
    values
  );
  return parseInt(result.rows[0]?.count || '0', 10);
}

/**
 * Type-safe insert query with returning
 */
export async function insert<T = unknown>(
  table: string,
  data: Record<string, unknown>,
  returning = '*'
): Promise<T> {
  const keys = Object.keys(data);
  const values = Object.values(data);
  const placeholders = keys.map((_, i) => `$${i + 1}`).join(', ');
  
  const text = `
    INSERT INTO ${table} (${keys.join(', ')})
    VALUES (${placeholders})
    RETURNING ${returning}
  `;
  
  return queryOneRequired<T>(text, values);
}

/**
 * Type-safe bulk insert
 */
export async function insertMany<T = unknown>(
  table: string,
  records: Record<string, unknown>[],
  returning = '*'
): Promise<T[]> {
  if (records.length === 0) return [];
  
  const keys = Object.keys(records[0]);
  const values: unknown[] = [];
  const valueStrings: string[] = [];
  
  records.forEach((record, recordIndex) => {
    const placeholders = keys.map((key, keyIndex) => {
      const valueIndex = recordIndex * keys.length + keyIndex + 1;
      values.push(record[key]);
      return `$${valueIndex}`;
    });
    valueStrings.push(`(${placeholders.join(', ')})`);
  });
  
  const text = `
    INSERT INTO ${table} (${keys.join(', ')})
    VALUES ${valueStrings.join(', ')}
    RETURNING ${returning}
  `;
  
  return queryMany<T>(text, values);
}

/**
 * Type-safe update query
 */
export async function update<T = unknown>(
  table: string,
  data: Record<string, unknown>,
  where: string,
  whereValues: unknown[] = [],
  returning = '*'
): Promise<T[]> {
  const keys = Object.keys(data);
  const values = Object.values(data);
  
  const setClause = keys
    .map((key, i) => `${key} = $${i + 1}`)
    .join(', ');
  
  const whereClause = where.replace(/\$(\d+)/g, (match, num) => {
    return `$${values.length + parseInt(num, 10)}`;
  });
  
  const text = `
    UPDATE ${table}
    SET ${setClause}
    WHERE ${whereClause}
    RETURNING ${returning}
  `;
  
  return queryMany<T>(text, [...values, ...whereValues]);
}

/**
 * Type-safe delete query
 */
export async function deleteRows<T = unknown>(
  table: string,
  where: string,
  values: unknown[] = [],
  returning = '*'
): Promise<T[]> {
  const text = `
    DELETE FROM ${table}
    WHERE ${where}
    RETURNING ${returning}
  `;
  
  return queryMany<T>(text, values);
}

/**
 * Type-safe transaction wrapper
 */
export async function transaction<T>(
  callback: (client: {
    query: <U = unknown>(
      text: string,
      values?: unknown[]
    ) => Promise<TypedQueryResult<U>>;
  }) => Promise<T>
): Promise<T> {
  const client = await db.connect();
  
  try {
    await client.query('BEGIN');
    
    const typedClient = {
      query: async <U = unknown>(
        text: string,
        values?: unknown[]
      ): Promise<TypedQueryResult<U>> => {
        const result = await client.query(text, values);
        return toTypedResult<U>(result);
      },
    };
    
    const result = await callback(typedClient);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Type-safe batch query execution
 */
export async function batch<T extends readonly unknown[]>(
  queries: {
    [K in keyof T]: {
      text: string;
      values?: unknown[];
    };
  }
): Promise<{ [K in keyof T]: TypedQueryResult<T[K]> }> {
  const results = await Promise.all(
    queries.map(({ text, values }) => db.query(text, values))
  );
  
  return results.map(toTypedResult) as {
    [K in keyof T]: TypedQueryResult<T[K]>;
  };
}

export default {
  query,
  queryOne,
  queryOneRequired,
  queryMany,
  exists,
  count,
  insert,
  insertMany,
  update,
  deleteRows,
  transaction,
  batch,
};