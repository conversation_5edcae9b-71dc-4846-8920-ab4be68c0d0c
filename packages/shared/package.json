{"name": "@spheroseg/shared", "version": "1.0.0", "description": "Shared utilities for SpheroSeg application", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/shared/src/index.d.ts", "default": "./dist/shared/src/index.js"}, "./utils/imageUtils": {"types": "./dist/shared/src/utils/imageUtils.d.ts", "default": "./dist/shared/src/utils/imageUtils.js"}, "./utils/pathUtils": {"types": "./dist/shared/src/utils/pathUtils.d.ts", "default": "./dist/shared/src/utils/pathUtils.js"}, "./utils/polygonWasmUtils": {"types": "./dist/shared/src/utils/polygonWasmUtils.d.ts", "default": "./dist/shared/src/utils/polygonWasmUtils.js"}, "./utils/polygonUtils": {"types": "./dist/shared/src/utils/polygonUtils.d.ts", "default": "./dist/shared/src/utils/polygonUtils.js"}, "./services/upload": {"types": "./dist/shared/src/services/upload/index.d.ts", "default": "./dist/shared/src/services/upload/index.js"}, "./utils/editModeManager": {"types": "./dist/shared/src/utils/editModeManager.d.ts", "default": "./dist/shared/src/utils/editModeManager.js"}, "./utils/polygonEventHandlers": {"types": "./dist/shared/src/utils/polygonEventHandlers.d.ts", "default": "./dist/shared/src/utils/polygonEventHandlers.js"}, "./src/monitoring": {"types": "./dist/shared/src/monitoring/index.d.ts", "default": "./dist/shared/src/monitoring/index.js"}}, "scripts": {"build": "tsc", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint --ext .ts,.tsx src", "format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,json}\"", "code:check": "npm run lint && npm run format:check", "code:fix": "npm run lint --fix && npm run format"}, "dependencies": {"@spheroseg/types": "file:../types", "isomorphic-dompurify": "^2.26.0", "pg": "^8.15.6", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^18.11.9", "@types/pg": "^8.15.4", "@types/uuid": "^9.0.8", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "dotenv": "^17.2.0", "happy-dom": "^12.10.3", "typescript": "^5.0.4", "vitest": "^1.0.4", "whatwg-fetch": "^3.6.2"}}