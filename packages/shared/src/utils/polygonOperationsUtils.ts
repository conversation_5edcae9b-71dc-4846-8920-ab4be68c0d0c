import { Point, Polygon } from '../types';

export interface WorkerRequest {
  id: string;
  operation: string;
  data: unknown;
}

export interface WorkerResponse {
  id: string;
  result?: unknown;
  error?: string;
}

export function isPointInPolygon(point: Point, polygon: Point[]): boolean {
  if (!polygon || polygon.length < 3) return false;

  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const pi = polygon[i];
    const pj = polygon[j];
    if (!pi || !pj) continue;

    const xi = pi.x;
    const yi = pi.y;
    const xj = pj.x;
    const yj = pj.y;

    const intersect =
      yi > point.y !== yj > point.y &&
      point.x < ((xj - xi) * (point.y - yi)) / (yj - yi) + xi;
    if (intersect) inside = !inside;
  }

  return inside;
}

export function slicePolygon(
  polygon: Polygon,
  _lineStart: Point,
  _lineEnd: Point
): { polygon1: Polygon; polygon2: Polygon } | null {
  // Simplified implementation
  return { polygon1: polygon, polygon2: polygon };
}

export function simplifyPolygon(
  polygon: Polygon,
  _tolerance: number = 1
): Polygon {
  // Return as-is for now
  return polygon;
}

export function calculatePolygonArea(points: Point[]): number {
  if (!points || points.length < 3) return 0;

  let area = 0;
  for (let i = 0; i < points.length; i++) {
    const j = (i + 1) % points.length;
    const pi = points[i];
    const pj = points[j];
    if (!pi || !pj) continue;

    area += pi.x * pj.y;
    area -= pj.x * pi.y;
  }
  return Math.abs(area / 2);
}

export function calculatePolygonPerimeter(points: Point[]): number {
  if (!points || points.length < 2) return 0;

  let perimeter = 0;
  for (let i = 0; i < points.length; i++) {
    const j = (i + 1) % points.length;
    const pi = points[i];
    const pj = points[j];
    if (!pi || !pj) continue;

    const dx = pj.x - pi.x;
    const dy = pj.y - pi.y;
    perimeter += Math.sqrt(dx * dx + dy * dy);
  }
  return perimeter;
}

export function calculateBoundingBox(points: Point[]): {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
} {
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }

  return { minX, minY, maxX, maxY };
}
