import { Polygon } from '../types';

export function sortPolygons(polygons: Polygon[]): Polygon[] {
  return [...polygons].sort((a, b) => {
    // Sort by type first (external before internal)
    if (a.type !== b.type) {
      return a.type === 'external' ? -1 : 1;
    }
    // Then by creation order or ID
    return a.id.localeCompare(b.id);
  });
}

export function simplifyPolygons(
  polygons: Polygon[],
  _tolerance: number = 1
): Polygon[] {
  // Simple polygon simplification - in production would use Douglas-Peucker
  return polygons;
}

export function separatePolygonsByType(polygons: Polygon[]) {
  const external = polygons.filter((p) => p.type === 'external');
  const internal = polygons.filter((p) => p.type === 'internal');
  return { external, internal };
}

export function createPolygonProps(
  polygon: Polygon,
  isSelected: boolean,
  isHovered: boolean
) {
  return {
    id: polygon.id,
    points: polygon.points,
    type: polygon.type,
    color: polygon.color,
    isSelected,
    isHovered,
    opacity: isSelected ? 1 : 0.8,
    strokeWidth: isSelected ? 2 : 1,
  };
}
