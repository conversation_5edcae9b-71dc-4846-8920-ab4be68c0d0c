import { Point, Polygon } from '../types';

export function slicePolygon(
  _polygon: Polygon,
  _lineStart: Point,
  _lineEnd: Point
): [Polygon, Polygon] | null {
  // Simplified polygon slicing - in production would use proper algorithm
  return null;
}

export function distanceToLineSegment(
  point: Point,
  lineStart: Point,
  lineEnd: Point
): number {
  const A = point.x - lineStart.x;
  const B = point.y - lineStart.y;
  const C = lineEnd.x - lineStart.x;
  const D = lineEnd.y - lineStart.y;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;
  let param = -1;

  if (lenSq !== 0) {
    param = dot / lenSq;
  }

  let xx, yy;

  if (param < 0) {
    xx = lineStart.x;
    yy = lineStart.y;
  } else if (param > 1) {
    xx = lineEnd.x;
    yy = lineEnd.y;
  } else {
    xx = lineStart.x + param * C;
    yy = lineStart.y + param * D;
  }

  const dx = point.x - xx;
  const dy = point.y - yy;

  return Math.sqrt(dx * dx + dy * dy);
}

export function createPolygon(points: Point[], id?: string): Polygon {
  return {
    id: id || `polygon-${Date.now()}`,
    points,
    type: 'external',
  };
}
