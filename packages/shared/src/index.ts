// Index file for @spheroseg/shared

// Export types
export * from './types';

// Export all polygon utilities
export {
  distance,
  calculateBoundingBox,
  calculateBoundingBoxRect,
  calculatePolygonArea,
  calculatePolygonPerimeter,
  calculateCentroid,
  isPointInPolygon,
  isPointInPolygonXY,
  getPointSideOfLine,
  perpendicularDistance,
  calculateIntersection,
  calculateLinePolygonIntersections,
  calculateConvexHull,
  isClockwise,
  ensureClockwise,
  ensureCounterClockwise,
  slicePolygon,
  slicePolygonObject,
  simplifyPolygon,
  simplifyClosedPolygon,
  doPolygonsIntersect,
  calculateFeretDiameter,
  calculateMetrics,
  isBoxVisible,
  executePolygonWorkerOperation,
  calculatePolygonAreaAsync,
  calculatePolygonPerimeterAsync,
  calculateBoundingBoxAsync,
  PolygonBoundingBoxCache,
  polygonBoundingBoxCache,
  createPolygon,
  clonePolygon,
  isValidPolygon,
  isPointInPolygonObj,
  calculateLineIntersection,
  getBoundingBox,
  getPolygonArea,
  getPolygonPerimeter,
  distanceToLineSegment,
} from './utils/polygonUtils';
export { default as polygonUtils } from './utils/polygonUtils';

// Export monitoring utilities
export * from './monitoring';

// Export segmentation status constants
export * from './constants/segmentationStatus';

// Export image utilities
export * from './utils/imageUtils';
export { default as imageUtils } from './utils/imageUtils';

// Export path utilities
export * from './utils/pathUtils';
export { default as pathUtils } from './utils/pathUtils';

// Export API response handling utilities
export * from './api';

// Export additional polygon utilities
export * from './utils/polygonOperationsUtils';
export * from './utils/polygonWorkerUtils';
export * from './utils/zoomHandlers';

// Export specific worker utilities
export { PolygonWasmProcessor as PolygonWorker } from './utils/polygonWasmUtils';
export { useZoomHandlers } from './utils/zoomHandlers';

// Export utility functions that are needed by frontend
export const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const createWorkerRequest = (operation: string, data: unknown): { id: string; operation: string; data: unknown } => {
  return {
    id: generateRequestId(),
    operation,
    data,
  };
};

export const createWorkerResponse = (id: string, result: unknown, error?: string): { id: string; result?: unknown; error?: string } => {
  return {
    id,
    result,
    error,
  };
};
