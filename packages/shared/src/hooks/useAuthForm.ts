import { useState } from 'react';

export interface AuthFormData {
  email: string;
  password: string;
}

export function useSignInForm() {
  const [isLoading, setIsLoading] = useState(false);

  const form = {
    // Mock form object - in real implementation this would use react-hook-form
    register: (name: string) => ({ name }),
    handleSubmit:
      (fn: (data: AuthFormData) => void) => (e: React.FormEvent) => {
        e.preventDefault();
        fn({ email: '', password: '' });
      },
    formState: {
      errors: {},
    },
  };

  const onSubmit = async (_data: AuthFormData) => {
    setIsLoading(true);
    // Mock submit - data is used here
    // In production, this would call the auth API
    // For now, just simulate async operation
    await new Promise((resolve) => setTimeout(resolve, 100));
    setIsLoading(false);
  };

  return {
    form,
    isLoading,
    onSubmit,
  };
}
