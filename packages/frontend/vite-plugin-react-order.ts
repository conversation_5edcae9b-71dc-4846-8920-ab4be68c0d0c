import type { Plugin } from 'vite';

/**
 * Vite plugin to ensure <PERSON>act is loaded before Radix UI components
 * This fixes the "Cannot read properties of undefined (reading 'forwardRef')" error
 */
export default function reactOrderPlugin(): Plugin {
  return {
    name: 'vite-plugin-react-order',
    enforce: 'pre',
    
    config() {
      return {
        optimizeDeps: {
          // Force React to be pre-bundled first
          entries: ['react', 'react-dom'],
          include: [
            'react',
            'react-dom',
            'react/jsx-runtime',
            'react/jsx-dev-runtime',
          ],
        },
      };
    },
    
    transform(code, id) {
      // Only process JavaScript/TypeScript files
      if (!id.match(/\.[jt]sx?$/)) {
        return null;
      }
      
      // For Radix UI components, ensure React is imported first
      if (id.includes('@radix-ui') && !id.includes('node_modules/.vite')) {
        // Check if the file uses forwardRef
        if (code.includes('forwardRef') && !code.includes("import React")) {
          // Add React import at the top if not present
          return {
            code: `import React from 'react';\n${code}`,
            map: null,
          };
        }
      }
      
      return null;
    },
    
    renderChunk(code, chunk) {
      // For production builds, ensure React is initialized before any Radix UI code
      if (chunk.fileName.includes('react-vendor')) {
        // Add initialization code at the beginning of the React vendor chunk
        const initCode = `
// React initialization safety check
(function() {
  if (typeof window !== 'undefined' && !window.React) {
    console.warn('React not found on window, attempting to initialize...');
  }
})();
`;
        return {
          code: initCode + code,
          map: null,
        };
      }
      
      return null;
    },
  };
}