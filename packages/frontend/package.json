{"name": "@spheroseg/frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --strictPort --port 3000", "build": "vite build", "build:dev": "vite build --mode development", "build:analyze": "ANALYZE=true vite build", "build:stats": "vite build --mode production -- --stats", "bundle:analyze": "vite-bundle-visualizer", "bundle:size": "size-limit", "analyze:bundle": "node scripts/analyze-bundle.js", "optimize:check": "npm run build && npm run analyze:bundle", "lint": "eslint .", "lint:fix": "npx eslint --version && npx eslint . --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss,json}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,css,scss,json}\"", "preview": "vite preview", "test": "vitest run --mode development", "test:integration": "vitest run --mode development --testPathPattern=integration", "test:unit": "vitest run --mode development --testPathIgnorePattern=integration", "test:watch": "vitest --mode development", "test:ui": "vitest --ui --mode development", "test:coverage": "vitest run --coverage --mode development", "test:ci": "vitest run --mode development --reporters=default --reporters=junit", "test:fast": "vitest run --mode development --pool=forks", "test:debug": "vitest run --mode development --pool=vmThreads", "test:match": "vitest run --mode development --pool=forks --run", "test:update": "vitest run --mode development --update", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:cached": "playwright test", "test:e2e:fresh": "CLEAR_TEST_CACHE=1 playwright test", "test:e2e:nocache": "DISABLE_TEST_CACHE=1 playwright test", "test:e2e:stats": "SHOW_CACHE_STATS=1 playwright test --list", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:visual": "scripts/visual-tests.sh", "test:visual:update": "scripts/visual-tests.sh --update", "test:visual:report": "playwright show-report playwright-report/visual", "clean": "rimraf dist node_modules .turbo .vitest/cache coverage test-results", "code:check": "npm run lint && npm run format:check", "code:fix": "npm run lint:fix && npm run format"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@spheroseg/shared": "file:../shared", "@spheroseg/types": "file:../types", "@tanstack/react-query": "^5.56.2", "@types/jimp": "^0.2.1", "@types/jspdf": "^1.3.3", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.4.1", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "framer-motion": "^12.5.0", "i18next": "^25.3.2", "jimp": "^1.6.0", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-i18next": "^15.5.1", "react-image-crop": "^11.0.10", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-window": "^1.8.11", "recharts": "^2.12.7", "regenerator-runtime": "^0.14.1", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "web-vitals": "^4.2.4", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@eslint/js": "^9.9.0", "@playwright/test": "^1.46.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.6.1", "@types/file-saver": "^2.0.7", "@types/multer": "^1.4.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.1.2", "autoprefixer": "^10.4.21", "axe-playwright": "^2.0.3", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^11.0.3", "identity-obj-proxy": "^3.0.0", "jest-image-snapshot": "^6.5.1", "madge": "^8.0.0", "msw": "^2.10.3", "postcss": "^8.5.3", "prettier": "^3.2.5", "resize-observer-polyfill": "^1.5.1", "rimraf": "^5.0.5", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.17", "terser": "^5.31.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-compression2": "^1.3.3", "vitest": "^3.1.3"}}