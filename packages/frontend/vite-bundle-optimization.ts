/**
 * Enhanced Bundle Optimization Plugin for Vite
 * 
 * Provides advanced optimization strategies for frontend bundle size reduction
 */

import type { Plugin, Rollup } from 'vite';
import { createHash } from 'crypto';

interface OptimizationOptions {
  /**
   * Enable aggressive code splitting
   */
  aggressiveSplitting?: boolean;

  /**
   * Minimum chunk size in bytes (default: 10KB)
   */
  minChunkSize?: number;

  /**
   * Maximum chunk size in bytes (default: 500KB)
   */
  maxChunkSize?: number;

  /**
   * Enable advanced tree shaking
   */
  advancedTreeShaking?: boolean;

  /**
   * Enable vendor chunk optimization
   */
  optimizeVendorChunks?: boolean;

  /**
   * Custom chunk priorities
   */
  chunkPriorities?: Record<string, number>;
}

/**
 * Calculate content hash for better caching
 */
function getContentHash(content: string): string {
  return createHash('sha256').update(content).digest('hex').substring(0, 8);
}

/**
 * Analyze import patterns for optimal chunking
 */
function analyzeImports(
  moduleInfo: Rollup.ModuleInfo,
  getModuleInfo: (id: string) => Rollup.ModuleInfo | null
): { frequency: number; size: number } {
  let frequency = 0;
  let totalSize = 0;

  const visited = new Set<string>();
  const queue = [moduleInfo.id];

  while (queue.length > 0) {
    const currentId = queue.shift()!;
    if (visited.has(currentId)) continue;
    visited.add(currentId);

    const info = getModuleInfo(currentId);
    if (!info) continue;

    frequency += info.importedIds.length;
    totalSize += info.code?.length || 0;

    // Add importers to analyze shared dependencies
    info.importers.forEach(importer => {
      if (!visited.has(importer)) {
        queue.push(importer);
      }
    });
  }

  return { frequency, size: totalSize };
}

/**
 * Enhanced manual chunks function with intelligent grouping
 */
function createManualChunks(options: OptimizationOptions) {
  const { minChunkSize = 10240, maxChunkSize = 512000, chunkPriorities = {} } = options;

  // Track module usage across chunks
  const moduleUsage = new Map<string, Set<string>>();
  const chunkSizes = new Map<string, number>();

  return function manualChunks(
    id: string,
    { getModuleInfo }: { getModuleInfo: (id: string) => Rollup.ModuleInfo | null }
  ): string | undefined {
    const moduleInfo = getModuleInfo(id);
    if (!moduleInfo) return;

    // Skip if module is too small
    if (moduleInfo.code && moduleInfo.code.length < 1000) {
      return; // Let Rollup handle small modules
    }

    // Vendor chunks with intelligent grouping
    if (id.includes('node_modules')) {
      // Critical React ecosystem - must be bundled together
      const reactEcosystem = [
        'react',
        'react-dom',
        'react-router',
        'scheduler',
        'object-assign',
        'prop-types',
      ];
      
      if (reactEcosystem.some(lib => id.includes(`/${lib}/`))) {
        return 'react-core';
      }

      // UI libraries that depend on React
      const uiLibraries = [
        '@radix-ui',
        '@floating-ui',
        'lucide-react',
        'sonner',
        'framer-motion',
        '@emotion',
        'styled-components',
        'vaul',
        'cmdk',
      ];

      if (uiLibraries.some(lib => id.includes(lib))) {
        return 'react-ui';
      }

      // Data fetching and state management
      const dataLibraries = [
        '@tanstack/react-query',
        'react-hook-form',
        '@hookform/resolvers',
        'zod',
        'axios',
        'socket.io-client',
      ];

      if (dataLibraries.some(lib => id.includes(lib))) {
        return 'data-layer';
      }

      // Utility libraries
      const utilityLibraries = [
        'date-fns',
        'clsx',
        'tailwind-merge',
        'class-variance-authority',
        'immer',
        'lodash',
      ];

      if (utilityLibraries.some(lib => id.includes(lib))) {
        return 'utils-vendor';
      }

      // Heavy computational libraries
      const heavyLibraries = [
        'polygon-clipping',
        '@turf',
        'martinez-polygon-clipping',
        'd3',
        'chart.js',
        'recharts',
      ];

      if (heavyLibraries.some(lib => id.includes(lib))) {
        return 'computational';
      }

      // Remaining vendor code
      return 'vendor-misc';
    }

    // Application code chunking based on features
    const appChunks = [
      { pattern: /\/auth\/|\/services\/auth/, chunk: 'auth' },
      { pattern: /\/project\/|\/services\/project/, chunk: 'project' },
      { pattern: /\/segmentation\//, chunk: 'segmentation' },
      { pattern: /\/export\//, chunk: 'export' },
      { pattern: /\/settings\/|\/profile\//, chunk: 'user-settings' },
      { pattern: /\/dashboard\/|\/analytics\//, chunk: 'dashboard' },
      { pattern: /\/components\/ui\//, chunk: 'ui-components' },
      { pattern: /\/hooks\//, chunk: 'hooks' },
      { pattern: /\/utils\//, chunk: 'utils' },
      { pattern: /\/api\/|\/services\/api/, chunk: 'api-client' },
    ];

    for (const { pattern, chunk } of appChunks) {
      if (pattern.test(id)) {
        // Check chunk size limits
        const currentSize = chunkSizes.get(chunk) || 0;
        const moduleSize = moduleInfo.code?.length || 0;

        if (currentSize + moduleSize > maxChunkSize) {
          // Create a new chunk variant
          let variant = 2;
          while (chunkSizes.has(`${chunk}-${variant}`)) {
            variant++;
          }
          const newChunk = `${chunk}-${variant}`;
          chunkSizes.set(newChunk, moduleSize);
          return newChunk;
        }

        chunkSizes.set(chunk, currentSize + moduleSize);
        return chunk;
      }
    }

    // Analyze module importance
    const { frequency, size } = analyzeImports(moduleInfo, getModuleInfo);
    
    // Shared modules (imported by 3+ modules)
    if (frequency >= 3) {
      return 'shared';
    }

    // Let Rollup handle the rest
    return;
  };
}

/**
 * Bundle optimization plugin
 */
export function bundleOptimizationPlugin(options: OptimizationOptions = {}): Plugin {
  return {
    name: 'vite-bundle-optimization',
    
    config(config) {
      // Enhance build configuration
      if (!config.build) config.build = {};
      
      config.build.rollupOptions = {
        ...config.build.rollupOptions,
        output: {
          ...config.build.rollupOptions?.output,
          
          // Use content hash for better caching
          entryFileNames: (chunkInfo) => {
            const hash = getContentHash(JSON.stringify(chunkInfo.exports));
            return `assets/js/[name]-${hash}.js`;
          },
          
          chunkFileNames: (chunkInfo) => {
            const hash = getContentHash(chunkInfo.name);
            return `assets/js/[name]-${hash}.js`;
          },
          
          // Manual chunks with intelligent grouping
          manualChunks: createManualChunks(options),
          
          // Optimize for modern browsers
          generatedCode: {
            preset: 'es2015',
            constBindings: true,
            objectShorthand: true,
            arrowFunctions: true,
            reservedNamesAsProps: false,
          },
        },
        
        // Tree shaking configuration
        treeshake: options.advancedTreeShaking ? {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          tryCatchDeoptimization: false,
          unknownGlobalSideEffects: false,
        } : true,
      };

      // Optimize dependencies
      if (!config.optimizeDeps) config.optimizeDeps = {};
      
      config.optimizeDeps = {
        ...config.optimizeDeps,
        
        // Include common dependencies
        include: [
          ...config.optimizeDeps.include || [],
          // Add any missing common dependencies
        ],
        
        // Exclude heavy dependencies that should be dynamically imported
        exclude: [
          ...config.optimizeDeps.exclude || [],
          // Computational libraries that should be lazy loaded
          'polygon-clipping',
          '@turf/*',
          'd3',
          'chart.js',
        ],
      };

      return config;
    },

    // Analyze and report bundle composition
    generateBundle(options, bundle) {
      const chunks = Object.values(bundle).filter(
        (asset): asset is Rollup.OutputChunk => asset.type === 'chunk'
      );

      // Generate bundle report
      const report = chunks.map(chunk => ({
        name: chunk.name,
        size: chunk.code.length,
        modules: Object.keys(chunk.modules).length,
        imports: chunk.imports,
        dynamicImports: chunk.dynamicImports,
      }));

      // Sort by size
      report.sort((a, b) => b.size - a.size);

      console.log('\n📦 Bundle Analysis Report:');
      console.log('========================');
      report.forEach(({ name, size, modules, imports, dynamicImports }) => {
        const sizeKB = (size / 1024).toFixed(2);
        console.log(`\n${name}:`);
        console.log(`  Size: ${sizeKB} KB`);
        console.log(`  Modules: ${modules}`);
        if (imports.length) console.log(`  Imports: ${imports.join(', ')}`);
        if (dynamicImports.length) console.log(`  Dynamic: ${dynamicImports.join(', ')}`);
      });

      const totalSize = report.reduce((sum, chunk) => sum + chunk.size, 0);
      console.log(`\nTotal Bundle Size: ${(totalSize / 1024).toFixed(2)} KB`);
      console.log('========================\n');
    },
  };
}

/**
 * Create prefetch links for critical chunks
 */
export function createPrefetchPlugin(): Plugin {
  return {
    name: 'vite-prefetch-plugin',
    
    transformIndexHtml(html) {
      // Add prefetch links for critical chunks
      const prefetchChunks = [
        'react-core',
        'react-ui',
        'data-layer',
        'auth',
        'project',
      ];

      const prefetchLinks = prefetchChunks
        .map(chunk => `<link rel="prefetch" href="/assets/js/${chunk}-[hash].js">`)
        .join('\n    ');

      // Inject prefetch links into head
      return html.replace('</head>', `    ${prefetchLinks}\n  </head>`);
    },
  };
}

export default bundleOptimizationPlugin;