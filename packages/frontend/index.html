<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SpheroSeg - Spheroid Segmentation Platform</title>
    <meta name="description" content="AI-powered cell analysis for biomedical research" />
    <meta name="author" content="SpheroSeg" />
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="expires" content="0" />

    <!-- Favicons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#2563EB" />

    <meta property="og:title" content="SpheroSeg - Spheroid Segmentation Platform" />
    <meta property="og:description" content="AI-powered cell analysis for biomedical research" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@spheroseg" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Loading screen styles -->
    <style>
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        text-align: center;
        padding: 2rem;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }

      #loading-screen.fade-out {
        opacity: 0;
        pointer-events: none;
      }

      #loading-content {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 500px;
        width: 100%;
      }

      #loading-spinner {
        width: 80px;
        height: 80px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 2rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      #loading-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        background: linear-gradient(45deg, #fff, #e0e7ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      #loading-subtitle {
        font-size: 1.2rem;
        margin: 0 0 1rem 0;
        opacity: 0.9;
      }

      #loading-status {
        font-size: 1rem;
        margin: 0;
        opacity: 0.7;
      }

      /* Hide root content until app loads */
      #root {
        display: none;
      }

      #root.loaded {
        display: block;
      }
    </style>

    <script>
      // Ensure React is available globally before any modules load
      window.__REACT_INIT__ = true;
      
      // Simple app initialization without React interference
      (function() {
        'use strict';
        
        // Show loading screen immediately
        window.addEventListener('DOMContentLoaded', function() {
          // Create loading screen if it doesn't exist
          if (!document.getElementById('loading-screen')) {
            const loadingScreen = document.createElement('div');
            loadingScreen.id = 'loading-screen';
            loadingScreen.innerHTML = `
              <div id="loading-content">
                <div id="loading-spinner"></div>
                <h1 id="loading-title">SpheroSeg</h1>
                <p id="loading-subtitle">Spheroid Segmentation Platform</p>
                <p id="loading-status">Loading application...</p>
              </div>
            `;
            document.body.appendChild(loadingScreen);
          }
        });

        // Remove loading screen when app is ready
        window.addEventListener('app-ready', function() {
          const loadingScreen = document.getElementById('loading-screen');
          const root = document.getElementById('root');
          
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
              loadingScreen.remove();
            }, 300);
          }
          
          if (root) {
            root.classList.add('loaded');
          }
        });

        // Fallback: Remove loading screen after timeout
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          const root = document.getElementById('root');
          
          if (loadingScreen && loadingScreen.parentNode) {
            console.warn('App loading timeout - removing loading screen');
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
              if (loadingScreen.parentNode) {
                loadingScreen.remove();
              }
            }, 300);
          }
          
          if (root) {
            root.classList.add('loaded');
          }
        }, 30000); // 30 second timeout - increased to allow app to load
      })();
    </script>
    
  </head>

  <body>
    <!-- Loading screen (shown immediately) -->
    <div id="loading-screen">
      <div id="loading-content">
        <div id="loading-spinner"></div>
        <h1 id="loading-title">SpheroSeg</h1>
        <p id="loading-subtitle">Spheroid Segmentation Platform</p>
        <p id="loading-status">Loading application...</p>
      </div>
    </div>

    <!-- App root (hidden until ready) -->
    <div id="root"></div>
    
    <!-- Vite will inject the module script here -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>