import { defineConfig, loadEnv, type PluginOption, type Plugin } from 'vite';
import react from '@vitejs/plugin-react';
import legacy from '@vitejs/plugin-legacy';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { compression } from 'vite-plugin-compression2';
import staticAssetsPlugin from './vite-static-fix';
import allowAllHostsPlugin from './vite-allowed-hosts-plugin';
import cleanIndexPlugin from './vite-plugin-clean-index';
import reactOrderPlugin from './vite-plugin-react-order';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env variables based on mode
  const env = loadEnv(mode, process.cwd(), '');
  const isDevelopment = mode === 'development';
  const isProduction = mode === 'production';

  // Determine API URL from environment or fallback to default
  const apiUrl = env.VITE_API_URL || 'http://localhost:5001';
  const apiBaseUrl = env.VITE_API_BASE_URL || '/api';
  // Adjust prefixes based on environment variables
  const apiAuthPrefix = env.VITE_API_AUTH_PREFIX ? `/api${env.VITE_API_AUTH_PREFIX}` : '/api/auth';
  const apiUsersPrefix = env.VITE_API_USERS_PREFIX ? `/api${env.VITE_API_USERS_PREFIX}` : '/api/users';

  console.log(`Using API URL: ${apiUrl} for proxy configuration`);
  console.log(`API Base URL: ${apiBaseUrl}`);
  console.log(`Auth Prefix: ${apiAuthPrefix}`);
  console.log(`Users Prefix: ${apiUsersPrefix}`);

  const plugins: PluginOption[] = [
    reactOrderPlugin(), // Ensure React loads before Radix UI
    react(),
    cleanIndexPlugin(), // Clean the index.html during build
    staticAssetsPlugin(),
    allowAllHostsPlugin(),
  ];

  // Add compression plugin for production
  if (isProduction) {
    plugins.push(
      compression({
        algorithm: 'gzip',
        exclude: [/\.(br)$/, /\.(gz)$/],
        threshold: 10240, // Only compress files larger than 10kb
      }) as unknown as Plugin,
      compression({
        algorithm: 'brotliCompress',
        exclude: [/\.(br)$/, /\.(gz)$/],
        threshold: 10240,
      }) as unknown as Plugin
    );
  }

  // Add bundle analyzer in production build with analyze flag
  if (process.env.ANALYZE) {
    plugins.push(
      visualizer({
        open: true,
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true,
      }) as unknown as Plugin
    );
  }

  return {
    plugins,
    // Define global constants
    define: {},
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@radix-ui/react-dialog',
        '@radix-ui/react-alert-dialog',
        '@radix-ui/react-dropdown-menu',
        'lucide-react',
        'sonner',
        '@tanstack/react-query',
        'socket.io-client',
        'react-hook-form',
        'zod',
        '@hookform/resolvers',
        '@spheroseg/types',
        '@spheroseg/shared',
      ],
      exclude: [],
      esbuildOptions: {
        target: 'es2020',
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@shared': path.resolve(__dirname, '../shared/src'),
        '@spheroseg/shared': path.resolve(__dirname, '../shared/src'),
        '@spheroseg/types': path.resolve(__dirname, '../types/src'),
      },
    },
    server: {
      watch: {
        ignored: [
          '**/assets/illustrations/**',
          '**/uploads/**',
          '**/node_modules/**',
        ],
        usePolling: true, // Use polling for Docker volumes
      },
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
      },
      fs: {
        strict: false, // Allow serving files from outside of root
        allow: ['..'],
      },

      proxy: {
        // Socket.IO proxy - highest priority to avoid conflicts
        '/socket.io': {
          target: apiUrl,
          changeOrigin: true,
          secure: false,
          ws: true,
          rewrite: (path) => path,
          configure: (proxy) => {
            proxy.on('error', (err) => {
              console.log('Socket.IO proxy error', err);
            });
            proxy.on('proxyReq', (_proxyReq, req) => {
              console.log('Socket.IO Request:', req.method, req.url);
            });
          },
        },
        // Main API proxy
        '/api': {
          target: apiUrl,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path,
          configure: (proxy) => {
            proxy.on('error', (err) => {
              console.log('API proxy error', err);
            });
            proxy.on('proxyReq', (_proxyReq, req) => {
              console.log('API Request:', req.method, req.url);
            });
            proxy.on('proxyRes', (proxyRes, req) => {
              console.log('API Response:', proxyRes.statusCode, req.url);
            });
          },
        },
        // File uploads
        '/uploads': {
          target: apiUrl,
          changeOrigin: true,
          secure: false,
        },
      },
      host: '0.0.0.0', // Allow access from any host
      port: 3000,
      strictPort: true, // Don't try other ports if 3000 is taken
      cors: {
        origin: true, // Allow all origins
        credentials: false,
      },
      origin: '*', // Allow all origins
      allowedHosts: ['.utia.cas.cz', 'localhost', 'spherosegapp.utia.cas.cz', 'frontend-dev', '0.0.0.0'],
      hmr: isDevelopment ? {
        port: 3000,
        host: 'localhost',
        protocol: 'ws', // Use ws:// in development to avoid SSL issues
      } : {
        // Production HMR through HTTPS
        clientPort: 443,
        protocol: 'wss',
        host: 'spherosegapp.utia.cas.cz',
        path: '/@hmr',
        timeout: 180000,
        overlay: true,
      },
    },
    // Optimize build
    build: {
      target: 'es2015', // Use older target for better compatibility
      minify: isProduction ? 'terser' : false, // Re-enable minification in production
      // Safer minification settings
      cssMinify: true,
      reportCompressedSize: false,
      chunkSizeWarningLimit: 5000,
      esbuildOptions: {
        target: 'es2015',
        keepNames: true, // Keep function names
        legalComments: 'none',
        pure: isProduction ? ['console.log', 'console.debug'] : [],
        format: 'esm',
      },
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? ['console.log', 'console.info', 'console.debug'] : [],
          // Ultra-safe settings to prevent TDZ issues
          ecma: 2015, // Use ES2015 to be extra safe
          toplevel: false,
          keep_fargs: true,
          keep_fnames: true,
          keep_classnames: true,
          // Disable ALL hoisting and unsafe optimizations
          hoist_funs: false,
          hoist_vars: false,
          hoist_props: false,
          inline: false, // Disable function inlining
          reduce_vars: false, // Don't reduce variables
          collapse_vars: false, // Don't collapse variables
          pure_getters: false, // Don't assume getters are pure
          unsafe: false, // Disable all unsafe optimizations
          unsafe_arrows: false,
          unsafe_comps: false,
          unsafe_Function: false,
          unsafe_math: false,
          unsafe_symbols: false,
          unsafe_methods: false,
          unsafe_proto: false,
          unsafe_regexp: false,
          unsafe_undefined: false,
          unused: false, // Don't remove unused code
          dead_code: false, // Don't remove dead code
        },
        mangle: {
          // Minimal mangling to prevent issues
          keep_fnames: true,
          keep_classnames: true,
          reserved: ['React', 'ReactDOM', 'useState', 'useEffect', 'useRef', 'useMemo', 'useCallback'],
          properties: false, // Don't mangle properties
        },
        format: {
          comments: false,
          ecma: 2015,
          ascii_only: true, // Use ASCII characters only
          beautify: false,
          braces: true,
          wrap_iife: true, // Wrap IIFEs
          wrap_func_args: false,
        },
        // Global options
        ecma: 2015,
        keep_classnames: true,
        keep_fnames: true,
        ie8: false,
        module: true,
        safari10: true, // Handle Safari 10 issues
      },
      sourcemap: isProduction ? 'hidden' : true, // Hidden source maps for production debugging
      rollupOptions: {
        // Disable tree shaking to prevent issues
        treeshake: false,
        external: [],
        output: {
          // Add timestamp to force cache invalidation
          entryFileNames: `assets/js/[name]-${Date.now()}-[hash].js`,
          chunkFileNames: `assets/js/[name]-${Date.now()}-[hash].js`,
          assetFileNames: `assets/[name]-${Date.now()}-[hash].[ext]`,
          // Use ES modules format
          format: 'es',
          generatedCode: {
            constBindings: false, // Use var instead of const to avoid TDZ
            arrowFunctions: false, // Don't use arrow functions
          },
          // Ensure proper module ordering
          hoistTransitiveImports: false,
          // Manual chunking for better caching and proper loading order
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              // Debug log to see what's being chunked
              if (id.includes('vendor-') || id.includes('utils-vendor')) {
                console.log('Checking module:', id);
              }
              
              // CRITICAL: Bundle React and ALL libraries that use React hooks into one chunk
              // to prevent "Cannot read properties of undefined" errors
              const reactLibraries = [
                'react', 'react-dom', 'react-router', 'scheduler',
                '@tanstack/react-query', 'react-hook-form', '@hookform/resolvers',
                'lucide-react', 'sonner', '@radix-ui', '@floating-ui',
                'framer-motion', '@emotion', 'styled-components',
                'react-i18', 'use-', '@use-', 'ahooks', 'usehooks',
                'vaul', 'cmdk', 'react-'
              ];
              
              // Check if this module should be in react-vendor
              if (reactLibraries.some(lib => id.includes(lib))) {
                return 'react-vendor';
              }
              
              // Everything else goes to vendor chunk
              return 'vendor';
            }
            
            // Feature-based chunks for our code
            if (id.includes('/pages/SignIn') || id.includes('/pages/SignUp') || 
                id.includes('/pages/ForgotPassword') || id.includes('/services/authService')) {
              return 'auth';
            }
            if (id.includes('/pages/ProjectDetail') || id.includes('/services/projectService') ||
                id.includes('/components/project')) {
              return 'project';
            }
            if (id.includes('/segmentation/')) {
              return 'segmentation';
            }
            if (id.includes('/export/') || id.includes('exportFunctions')) {
              return 'export';
            }
            if (id.includes('/pages/Settings') || id.includes('/pages/Profile')) {
              return 'settings';
            }
            if (id.includes('/pages/Dashboard') || id.includes('/components/analytics')) {
              return 'dashboard';
            }
          },
        },
      },
      // Enable CSS code splitting
      cssCodeSplit: true,
      // Inline assets smaller than 4kb
      assetsInlineLimit: 4096,
    },
    // Configure base path for production
    base: '/',
    // Improve error handling
    logLevel: 'info',
    // Enable JSON loading
    json: {
      namedExports: true,
      stringify: false,
    },
    // CSS configuration
    css: {
      modules: {
        localsConvention: 'camelCase',
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`,
        },
      },
    },
  };
});