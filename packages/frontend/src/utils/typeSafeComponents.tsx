/**
 * Type-Safe Component Utilities
 * 
 * Provides strongly typed alternatives to common React patterns
 * that often use 'any' types
 */

import React, { ComponentType, LazyExoticComponent, ReactElement } from 'react';

/**
 * Strictly typed lazy component wrapper
 */
export function createLazyComponent<T extends ComponentType<unknown>>(
  importFn: () => Promise<{ default: T }>
): LazyExoticComponent<T> {
  return React.lazy(importFn);
}

/**
 * Type-safe component props extraction
 */
export type ComponentProps<T> = T extends ComponentType<infer P> ? P : never;

/**
 * Type-safe higher-order component
 */
export type HOC<TInner, TOuter = TInner> = (
  Component: ComponentType<TInner>
) => ComponentType<TOuter>;

/**
 * Create a type-safe HOC
 */
export function createHOC<TInner, TOuter = TInner>(
  hocName: string,
  hocImpl: (Component: ComponentType<TInner>) => ComponentType<TOuter>
): HOC<TInner, TOuter> {
  return (Component: ComponentType<TInner>) => {
    const WrappedComponent = hocImpl(Component);
    WrappedComponent.displayName = `${hocName}(${Component.displayName || Component.name || 'Component'})`;
    return WrappedComponent;
  };
}

/**
 * Type-safe children clone utility
 */
export function cloneChildrenWithProps<P extends object>(
  children: React.ReactNode,
  props: P
): React.ReactNode {
  return React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, props as Partial<typeof child.props>);
    }
    return child;
  });
}

/**
 * Type-safe render prop pattern
 */
export interface RenderProp<TData> {
  children: (data: TData) => ReactElement;
}

export interface RenderPropComponent<TData, TProps = {}> {
  (props: TProps & RenderProp<TData>): ReactElement;
}

/**
 * Create a type-safe render prop component
 */
export function createRenderPropComponent<TData, TProps = {}>(
  useData: (props: TProps) => TData
): RenderPropComponent<TData, TProps> {
  return function RenderPropComponent({ children, ...props }: TProps & RenderProp<TData>) {
    const data = useData(props as TProps);
    return children(data);
  };
}

/**
 * Type-safe context creation
 */
export interface TypedContext<T> {
  Provider: React.Provider<T>;
  Consumer: React.Consumer<T>;
  useContext: () => T;
}

export function createTypedContext<T>(
  defaultValue: T,
  displayName?: string
): TypedContext<T> {
  const Context = React.createContext<T>(defaultValue);
  
  if (displayName) {
    Context.displayName = displayName;
  }

  const useContext = () => {
    const context = React.useContext(Context);
    if (context === undefined) {
      throw new Error(
        `use${displayName || 'Context'} must be used within a ${displayName || 'Context'}.Provider`
      );
    }
    return context;
  };

  return {
    Provider: Context.Provider,
    Consumer: Context.Consumer,
    useContext,
  };
}

/**
 * Type-safe forward ref component
 */
export type ForwardRefComponent<TRef, TProps> = React.ForwardRefExoticComponent<
  TProps & React.RefAttributes<TRef>
>;

export function createForwardRefComponent<TRef, TProps>(
  displayName: string,
  render: (props: TProps, ref: React.Ref<TRef>) => ReactElement | null
): ForwardRefComponent<TRef, TProps> {
  const Component = React.forwardRef(render);
  Component.displayName = displayName;
  return Component;
}

/**
 * Type-safe memo component
 */
export function createMemoComponent<T extends ComponentType<unknown>>(
  Component: T,
  propsAreEqual?: (
    prevProps: Readonly<ComponentProps<T>>,
    nextProps: Readonly<ComponentProps<T>>
  ) => boolean
): T {
  const MemoComponent = React.memo(Component, propsAreEqual) as T;
  MemoComponent.displayName = `Memo(${Component.displayName || Component.name || 'Component'})`;
  return MemoComponent;
}

/**
 * Type-safe error boundary props
 */
export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export interface ErrorBoundaryProps {
  fallback: (error: Error, resetError: () => void) => ReactElement;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  children: React.ReactNode;
}

/**
 * Type-safe error boundary
 */
export class TypedErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    this.props.onError?.(error, errorInfo);
  }

  resetError = (): void => {
    this.setState({ hasError: false, error: null });
  };

  render(): React.ReactNode {
    if (this.state.hasError && this.state.error) {
      return this.props.fallback(this.state.error, this.resetError);
    }

    return this.props.children;
  }
}

/**
 * Type-safe component composition
 */
export function composeComponents<T extends ComponentType<unknown>[]>(
  ...components: T
): ComponentType<ComponentProps<T[number]>> {
  return components.reduce(
    (AccumulatedComponents, CurrentComponent) =>
      function ComposedComponent(props: ComponentProps<T[number]>) {
        return (
          <AccumulatedComponents {...props}>
            <CurrentComponent {...props} />
          </AccumulatedComponents>
        );
      },
    ({ children }) => <>{children}</> as ReactElement
  ) as ComponentType<ComponentProps<T[number]>>;
}

/**
 * Type-safe props with children
 */
export type PropsWithChildren<P = unknown> = P & { children?: React.ReactNode };

/**
 * Type-safe props with className
 */
export type PropsWithClassName<P = unknown> = P & { className?: string };

/**
 * Type-safe props with style
 */
export type PropsWithStyle<P = unknown> = P & { style?: React.CSSProperties };

/**
 * Common prop combinations
 */
export type CommonProps<P = unknown> = PropsWithChildren<
  PropsWithClassName<PropsWithStyle<P>>
>;