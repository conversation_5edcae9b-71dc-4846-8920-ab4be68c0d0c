/**
 * React initialization module
 * Ensures React is properly initialized before any components that depend on it
 */

import React from 'react';
import ReactDOM from 'react-dom';

// Ensure React is available globally before any other imports
if (typeof window !== 'undefined') {
  // Check if we're in the initialization phase
  if (!(window as any).__REACT_INIT__) {
    console.warn('React initialization may be too late - forwardRef errors possible');
  }
  
  // Store React on window to ensure it's available
  (window as any).React = React;
  (window as any).ReactDOM = ReactDOM;
  
  // Pre-initialize commonly used React APIs
  const { forwardRef, createContext, useContext, useState, useEffect } = React;
  
  // Store these on window as well for emergency access
  (window as any).ReactForwardRef = forwardRef;
  (window as any).ReactCreateContext = createContext;
  (window as any).ReactUseContext = useContext;
  (window as any).ReactUseState = useState;
  (window as any).ReactUseEffect = useEffect;
}

// Export React to ensure it's available
export { React, ReactDOM };
export default React;