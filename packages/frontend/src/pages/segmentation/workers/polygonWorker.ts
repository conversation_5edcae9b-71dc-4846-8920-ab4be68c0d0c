// This is a Web Worker for polygon operations
// It handles computationally intensive tasks like:
// - Polygon intersection detection
// - Slicing polygons
// - Simplifying polygons
// - Calculating polygon area and perimeter

import {
  WorkerRequest,
  WorkerResponse,
  isPointInPolygon,
  slicePolygon,
  simplifyPolygon,
  calculatePolygonArea,
  calculatePolygonPerimeter,
  calculateBoundingBox,
} from '@spheroseg/shared';

// Handle messages from the main thread
self.onmessage = (event: MessageEvent<WorkerRequest>) => {
  const { id, operation, data } = event.data;

  try {
    let result;

    switch (operation) {
      case 'isPointInPolygon': {
        const typedData = data as { point: { x: number; y: number }; polygon: Array<{ x: number; y: number }> };
        result = isPointInPolygon(typedData.point, typedData.polygon);
        break;
      }

      case 'slicePolygon': {
        const typedData = data as { polygon: Array<{ x: number; y: number }>; sliceStart: { x: number; y: number }; sliceEnd: { x: number; y: number } };
        result = slicePolygon(typedData.polygon, typedData.sliceStart, typedData.sliceEnd);
        break;
      }

      case 'simplifyPolygon': {
        const typedData = data as { polygon: Array<{ x: number; y: number }>; epsilon: number };
        result = simplifyPolygon(typedData.polygon, typedData.epsilon);
        break;
      }

      case 'calculatePolygonArea': {
        const typedData = data as { polygon: Array<{ x: number; y: number }> };
        result = calculatePolygonArea(typedData.polygon);
        break;
      }

      case 'calculatePolygonPerimeter': {
        const typedData = data as { polygon: Array<{ x: number; y: number }> };
        result = calculatePolygonPerimeter(typedData.polygon);
        break;
      }

      case 'calculateBoundingBox': {
        const typedData = data as { polygon: Array<{ x: number; y: number }> };
        result = calculateBoundingBox(typedData.polygon);
        break;
      }

      default:
        throw new Error(`Unknown operation: ${operation}`);
    }

    const response: WorkerResponse = {
      id,
      result,
    };

    self.postMessage(response);
  } catch (_error) {
    const response: WorkerResponse = {
      id,
      error: _error instanceof Error ? _error.message : String(_error),
    };

    self.postMessage(response);
  }
};

// Export empty object to satisfy TypeScript
export {};
