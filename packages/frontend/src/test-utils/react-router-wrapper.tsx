import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Router<PERSON><PERSON><PERSON>, createM<PERSON>oryRouter } from 'react-router-dom';

// Ensure React Router future flags are set
// Disable the warnings in React Router
process.env.REACT_ROUTER_SKIP_WARNINGS = 'true';
window.REACT_ROUTER_FUTURE_FLAGS = {
  v7_startTransition: true,
  v7_relativeSplatPath: true,
  v7_normalizeFormMethod: true,
};

/**
 * Test router wrapper that sets the future flags
 * Use this in tests instead of directly using BrowserRouter
 */
export const TestRouterWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Ensure flags are set before rendering
  if (!window.REACT_ROUTER_FUTURE_FLAGS) {
    window.REACT_ROUTER_FUTURE_FLAGS = {
      v7_startTransition: true,
      v7_relativeSplatPath: true,
      v7_normalizeFormMethod: true,
    };
  }

  return <BrowserRouter>{children}</BrowserRouter>;
};

/**
 * Memory router wrapper for tests
 */
export const MemoryRouterWrapper: React.FC<{ children: React.ReactNode; initialEntries?: string[] }> = ({ 
  children, 
  initialEntries = ['/'] 
}) => {
  // Ensure flags are set before rendering
  if (!window.REACT_ROUTER_FUTURE_FLAGS) {
    window.REACT_ROUTER_FUTURE_FLAGS = {
      v7_startTransition: true,
      v7_relativeSplatPath: true,
      v7_normalizeFormMethod: true,
    };
  }

  return <MemoryRouter initialEntries={initialEntries}>{children}</MemoryRouter>;
};

/**
 * Create a memory router with future flags for testing
 */
export const createTestRouter = (routes: unknown[], initialEntries = ['/', '/about']) => {
  return createMemoryRouter(routes, {
    initialEntries,
    future: {
      v7_relativeSplatPath: true,
      v7_normalizeFormMethod: true,
      v7_fetcherPersist: true,
      v7_partialHydration: true,
      v7_skipActionErrorRevalidation: true,
    } as any, // Type assertion to handle version differences
  });
};

/**
 * Test router provider that sets the future flags
 */
export const TestRouterProvider: React.FC<{
  router: ReturnType<typeof createMemoryRouter>;
}> = ({ router }) => {
  // Ensure flags are set before rendering
  if (!window.REACT_ROUTER_FUTURE_FLAGS) {
    window.REACT_ROUTER_FUTURE_FLAGS = {
      v7_startTransition: true,
      v7_relativeSplatPath: true,
      v7_normalizeFormMethod: true,
    };
  }

  return <RouterProvider router={router} />;
};
