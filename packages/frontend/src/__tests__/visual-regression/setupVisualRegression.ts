import { test as base, expect as baseExpect } from '@playwright/test';
import { toMatchImageSnapshot } from 'jest-image-snapshot';
import * as path from 'path';
import * as fs from 'fs';

// Extend Playwright's expect with image snapshot matcher
baseExpect.extend({ toMatchImageSnapshot });

// Export the extended expect
export const expect = baseExpect;

/**
 * Configuration for visual regression testing
 */
export const visualConfig = {
  snapshotsDir: path.join(__dirname, 'snapshots'),
  threshold: 0.02, // Allow 2% pixel difference

  // Visual comparison options
  comparisonOptions: {
    failureThreshold: 0.02,
    failureThresholdType: 'percent',
    updatePassedSnapshot: false,
    blur: 1, // Apply slight blur to reduce noise
    allowSizeMismatch: false,
  },
};

// Create snapshots directory if it doesn't exist
if (!fs.existsSync(visualConfig.snapshotsDir)) {
  fs.mkdirSync(visualConfig.snapshotsDir, { recursive: true });
}

/**
 * Custom test fixture with visual regression testing capabilities
 */
export const test = base.extend({
  page: async ({ page }, use) => {
    // Add a helper method to take and compare screenshots
    (page as any).compareScreenshot = async (name: string, options = {}) => {
      // Take screenshot
      const screenshot = await page.screenshot({
        fullPage: false,
        ...options,
      });

      // Construct snapshot path
      const snapshotPath = path.join(visualConfig.snapshotsDir, `${name}.png`);

      // If snapshot doesn't exist yet, save it
      if (!fs.existsSync(snapshotPath) && process.env.UPDATE_VISUAL_SNAPSHOTS) {
        fs.writeFileSync(snapshotPath, screenshot);

        return;
      }

      // Compare with existing snapshot
      (expect(screenshot) as any).toMatchImageSnapshot({
        customSnapshotIdentifier: name,
        customSnapshotsDir: visualConfig.snapshotsDir,
        ...visualConfig.comparisonOptions,
      });
    };

    // Add a method to compare a specific element
    (page as any).compareElement = async (selector: string, name: string, options = {}) => {
      const element = await page.$(selector);
      if (!element) {
        throw new Error(`Element not found: ${selector}`);
      }

      const screenshot = await element.screenshot(options);

      // Construct snapshot path
      const snapshotPath = path.join(visualConfig.snapshotsDir, `${name}.png`);

      // If snapshot doesn't exist yet, save it
      if (!fs.existsSync(snapshotPath) && process.env.UPDATE_VISUAL_SNAPSHOTS) {
        fs.writeFileSync(snapshotPath, screenshot);

        return;
      }

      // Compare with existing snapshot
      (expect(screenshot) as any).toMatchImageSnapshot({
        customSnapshotIdentifier: name,
        customSnapshotsDir: visualConfig.snapshotsDir,
        ...visualConfig.comparisonOptions,
      });
    };

    await use(page);
  },
});

// Type the extended page methods
interface ExtendedPage {
  compareScreenshot(name: string, options?: unknown): Promise<void>;
  compareElement(selector: string, name: string, options?: unknown): Promise<void>;
}
