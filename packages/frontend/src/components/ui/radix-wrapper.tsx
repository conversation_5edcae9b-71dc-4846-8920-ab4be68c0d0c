/**
 * Wrapper to ensure React is available for Radix UI components
 * This helps prevent the forwardRef errors in production builds
 */

import React from 'react';

// Ensure React.forwardRef is available
if (typeof window !== 'undefined' && !React.forwardRef) {
  console.error('React.forwardRef is not available! This will cause Radix UI to fail.');
}

// Re-export React to ensure it's available
export { React };

// Helper function to safely create forwardRef components
export function safeForwardRef<T, P = {}>(
  render: React.ForwardRefRenderFunction<T, P>
): React.ForwardRefExoticComponent<React.PropsWithoutRef<P> & React.RefAttributes<T>> {
  if (!React.forwardRef) {
    console.error('React.forwardRef is not available!');
    // Return a fallback component that doesn't use forwardRef
    return React.memo((props: P) => {
      return render(null as any, props);
    }) as any;
  }
  return React.forwardRef(render);
}

// Initialize React APIs that Radix UI depends on
export const ReactAPIs = {
  forwardRef: React.forwardRef,
  createContext: React.createContext,
  useContext: React.useContext,
  useState: React.useState,
  useEffect: React.useEffect,
  useRef: React.useRef,
  useMemo: React.useMemo,
  useCallback: React.useCallback,
  useLayoutEffect: React.useLayoutEffect,
};

// Verify all APIs are available
Object.entries(ReactAPIs).forEach(([name, api]) => {
  if (!api) {
    console.error(`React.${name} is not available!`);
  }
});