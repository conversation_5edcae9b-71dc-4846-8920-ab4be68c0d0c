import React from 'react';
import { Button } from './button';
import { Calendar } from 'lucide-react';

interface DateRange {
  from?: Date;
  to?: Date;
}

interface DatePickerWithRangeProps {
  value?: DateRange;
  onChange?: (range: DateRange | undefined) => void;
  className?: string;
}

export function DatePickerWithRange({ value, onChange, className }: DatePickerWithRangeProps) {
  return (
    <Button variant="outline" className={className}>
      <Calendar className="mr-2 h-4 w-4" />
      <span>Pick a date range</span>
    </Button>
  );
}
