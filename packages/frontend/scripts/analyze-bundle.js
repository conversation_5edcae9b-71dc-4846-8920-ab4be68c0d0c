#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * 
 * Analyzes the production bundle and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');
const { gzipSync, brotliCompressSync } = require('zlib');
const chalk = require('chalk');

// Configuration
const DIST_DIR = path.join(__dirname, '../dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');
const SIZE_THRESHOLDS = {
  CRITICAL: 50 * 1024,    // 50KB - Critical chunks should be under this
  WARNING: 200 * 1024,    // 200KB - Large chunks need attention
  MAX: 500 * 1024,        // 500KB - Maximum recommended chunk size
};

/**
 * Get all JS files from the dist directory
 */
function getJsFiles() {
  const jsFiles = [];
  
  function scanDir(dir) {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDir(filePath);
      } else if (file.endsWith('.js')) {
        jsFiles.push(filePath);
      }
    });
  }
  
  scanDir(ASSETS_DIR);
  return jsFiles;
}

/**
 * Analyze a single file
 */
function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  const stats = fs.statSync(filePath);
  
  const analysis = {
    path: filePath,
    name: path.basename(filePath),
    size: stats.size,
    gzipSize: gzipSync(content).length,
    brotliSize: brotliCompressSync(content).length,
    content: content,
  };
  
  // Extract chunk type from filename
  const chunkMatch = analysis.name.match(/^([^-]+)/);
  analysis.chunkType = chunkMatch ? chunkMatch[1] : 'unknown';
  
  // Analyze content
  analysis.hasSourceMap = content.includes('//# sourceMappingURL=');
  analysis.isVendor = analysis.name.includes('vendor') || analysis.name.includes('node_modules');
  
  // Count imports/exports
  const importMatches = content.match(/import\s+.*from/g) || [];
  const exportMatches = content.match(/export\s+/g) || [];
  analysis.imports = importMatches.length;
  analysis.exports = exportMatches.length;
  
  // Detect libraries
  analysis.libraries = detectLibraries(content);
  
  return analysis;
}

/**
 * Detect common libraries in the bundle
 */
function detectLibraries(content) {
  const libraries = [];
  const patterns = [
    { name: 'React', pattern: /React\.createElement|useState|useEffect/ },
    { name: 'React Router', pattern: /useNavigate|useParams|RouterProvider/ },
    { name: 'Radix UI', pattern: /@radix-ui/ },
    { name: 'Tanstack Query', pattern: /@tanstack\/react-query|useQuery|useMutation/ },
    { name: 'Socket.io', pattern: /socket\.io-client|io\(/ },
    { name: 'Axios', pattern: /axios\.create|axios\.get/ },
    { name: 'Zod', pattern: /z\.object|z\.string/ },
    { name: 'Lucide Icons', pattern: /lucide-react/ },
    { name: 'Tailwind', pattern: /tailwind|tw-/ },
    { name: 'Framer Motion', pattern: /framer-motion|motion\./ },
  ];
  
  patterns.forEach(({ name, pattern }) => {
    if (pattern.test(content)) {
      libraries.push(name);
    }
  });
  
  return libraries;
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations(analyses) {
  const recommendations = [];
  
  // Check for oversized chunks
  analyses.forEach(analysis => {
    if (analysis.size > SIZE_THRESHOLDS.MAX) {
      recommendations.push({
        severity: 'critical',
        file: analysis.name,
        message: `Chunk exceeds maximum size (${formatSize(analysis.size)} > ${formatSize(SIZE_THRESHOLDS.MAX)})`,
        suggestion: 'Split this chunk into smaller pieces using dynamic imports',
      });
    } else if (analysis.size > SIZE_THRESHOLDS.WARNING) {
      recommendations.push({
        severity: 'warning',
        file: analysis.name,
        message: `Large chunk size (${formatSize(analysis.size)})`,
        suggestion: 'Consider lazy loading non-critical parts',
      });
    }
  });
  
  // Check for duplicate libraries across chunks
  const libraryOccurrences = {};
  analyses.forEach(analysis => {
    analysis.libraries.forEach(lib => {
      if (!libraryOccurrences[lib]) {
        libraryOccurrences[lib] = [];
      }
      libraryOccurrences[lib].push(analysis.name);
    });
  });
  
  Object.entries(libraryOccurrences).forEach(([lib, files]) => {
    if (files.length > 2) {
      recommendations.push({
        severity: 'warning',
        file: 'Multiple',
        message: `${lib} appears in ${files.length} chunks`,
        suggestion: `Consider extracting ${lib} to a shared chunk`,
      });
    }
  });
  
  // Check compression ratios
  analyses.forEach(analysis => {
    const compressionRatio = analysis.gzipSize / analysis.size;
    if (compressionRatio > 0.4) {
      recommendations.push({
        severity: 'info',
        file: analysis.name,
        message: `Poor compression ratio (${(compressionRatio * 100).toFixed(1)}%)`,
        suggestion: 'Code might already be minified or contains large data structures',
      });
    }
  });
  
  return recommendations;
}

/**
 * Format file size
 */
function formatSize(bytes) {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
}

/**
 * Print analysis results
 */
function printResults(analyses, recommendations) {
  console.log(chalk.bold('\n📊 Bundle Analysis Report\n'));
  console.log('=' .repeat(80));
  
  // Sort by size
  analyses.sort((a, b) => b.size - a.size);
  
  // Print chunk analysis
  console.log(chalk.bold('\n📦 Chunk Analysis:\n'));
  
  const chunkGroups = {};
  analyses.forEach(analysis => {
    if (!chunkGroups[analysis.chunkType]) {
      chunkGroups[analysis.chunkType] = [];
    }
    chunkGroups[analysis.chunkType].push(analysis);
  });
  
  Object.entries(chunkGroups).forEach(([type, chunks]) => {
    const totalSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
    const totalGzip = chunks.reduce((sum, chunk) => sum + chunk.gzipSize, 0);
    
    console.log(chalk.cyan(`\n${type.toUpperCase()} Chunks:`));
    console.log(`  Total: ${formatSize(totalSize)} (${formatSize(totalGzip)} gzipped)`);
    console.log(`  Files: ${chunks.length}`);
    
    chunks.forEach(chunk => {
      const sizeColor = chunk.size > SIZE_THRESHOLDS.WARNING ? chalk.red :
                       chunk.size > SIZE_THRESHOLDS.CRITICAL ? chalk.yellow :
                       chalk.green;
      
      console.log(`    ${chunk.name}:`);
      console.log(`      Size: ${sizeColor(formatSize(chunk.size))}`);
      console.log(`      Gzip: ${formatSize(chunk.gzipSize)} (${((chunk.gzipSize / chunk.size) * 100).toFixed(1)}%)`);
      console.log(`      Brotli: ${formatSize(chunk.brotliSize)} (${((chunk.brotliSize / chunk.size) * 100).toFixed(1)}%)`);
      if (chunk.libraries.length) {
        console.log(`      Libraries: ${chunk.libraries.join(', ')}`);
      }
    });
  });
  
  // Print totals
  const totalSize = analyses.reduce((sum, a) => sum + a.size, 0);
  const totalGzip = analyses.reduce((sum, a) => sum + a.gzipSize, 0);
  const totalBrotli = analyses.reduce((sum, a) => sum + a.brotliSize, 0);
  
  console.log(chalk.bold('\n📈 Bundle Totals:\n'));
  console.log(`  Raw: ${formatSize(totalSize)}`);
  console.log(`  Gzip: ${formatSize(totalGzip)} (${((totalGzip / totalSize) * 100).toFixed(1)}% compression)`);
  console.log(`  Brotli: ${formatSize(totalBrotli)} (${((totalBrotli / totalSize) * 100).toFixed(1)}% compression)`);
  
  // Print recommendations
  if (recommendations.length > 0) {
    console.log(chalk.bold('\n💡 Optimization Recommendations:\n'));
    
    const grouped = {
      critical: recommendations.filter(r => r.severity === 'critical'),
      warning: recommendations.filter(r => r.severity === 'warning'),
      info: recommendations.filter(r => r.severity === 'info'),
    };
    
    if (grouped.critical.length) {
      console.log(chalk.red('\nCritical:'));
      grouped.critical.forEach(rec => {
        console.log(`  • [${rec.file}] ${rec.message}`);
        console.log(`    → ${rec.suggestion}`);
      });
    }
    
    if (grouped.warning.length) {
      console.log(chalk.yellow('\nWarnings:'));
      grouped.warning.forEach(rec => {
        console.log(`  • [${rec.file}] ${rec.message}`);
        console.log(`    → ${rec.suggestion}`);
      });
    }
    
    if (grouped.info.length) {
      console.log(chalk.blue('\nInfo:'));
      grouped.info.forEach(rec => {
        console.log(`  • [${rec.file}] ${rec.message}`);
        console.log(`    → ${rec.suggestion}`);
      });
    }
  } else {
    console.log(chalk.green('\n✅ No major optimization issues found!'));
  }
  
  console.log('\n' + '='.repeat(80) + '\n');
}

/**
 * Main execution
 */
function main() {
  console.log(chalk.bold('🔍 Analyzing production bundle...\n'));
  
  // Check if dist directory exists
  if (!fs.existsSync(DIST_DIR)) {
    console.error(chalk.red('Error: dist directory not found. Run "npm run build" first.'));
    process.exit(1);
  }
  
  // Get all JS files
  const jsFiles = getJsFiles();
  
  if (jsFiles.length === 0) {
    console.error(chalk.red('Error: No JavaScript files found in dist directory.'));
    process.exit(1);
  }
  
  // Analyze each file
  const analyses = jsFiles.map(analyzeFile);
  
  // Generate recommendations
  const recommendations = generateRecommendations(analyses);
  
  // Print results
  printResults(analyses, recommendations);
  
  // Save detailed report
  const reportPath = path.join(DIST_DIR, 'bundle-analysis.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    analyses,
    recommendations,
    summary: {
      totalFiles: analyses.length,
      totalSize: analyses.reduce((sum, a) => sum + a.size, 0),
      totalGzipSize: analyses.reduce((sum, a) => sum + a.gzipSize, 0),
      totalBrotliSize: analyses.reduce((sum, a) => sum + a.brotliSize, 0),
    },
  }, null, 2));
  
  console.log(chalk.gray(`Detailed report saved to: ${reportPath}`));
}

// Run the analysis
main();